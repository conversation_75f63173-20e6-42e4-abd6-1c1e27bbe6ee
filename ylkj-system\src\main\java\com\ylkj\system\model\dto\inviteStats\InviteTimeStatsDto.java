package com.ylkj.system.model.dto.inviteStats;

import lombok.Data;
import java.io.Serializable;

/**
 * 邀请码时间维度统计DTO
 * 
 * <AUTHOR>
 * @date 2025-01-23
 */
@Data
public class InviteTimeStatsDto implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /** 时间标签（日期/周/月） */
    private String timeLabel;
    
    /** 访问次数 */
    private Long visitCount;
    
    /** 注册次数 */
    private Long registrationCount;
    
    /** 转化率 */
    private Double conversionRate;
}
