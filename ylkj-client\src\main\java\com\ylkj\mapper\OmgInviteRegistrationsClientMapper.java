package com.ylkj.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ylkj.model.domain.OmgInviteRegistrations;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 邀请码注册记录客户端Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-23
 */
@Mapper
public interface OmgInviteRegistrationsClientMapper extends BaseMapper<OmgInviteRegistrations> {
    
    /**
     * 检查用户是否已通过该邀请码注册过
     * 
     * @param inviteCode 邀请码
     * @param userId 用户ID
     * @return 记录数量
     */
    @Select("SELECT COUNT(*) FROM omg_invite_registrations WHERE invite_code = #{inviteCode} AND user_id = #{userId}")
    int checkUserRegistration(@Param("inviteCode") String inviteCode, 
                             @Param("userId") Long userId);
}
