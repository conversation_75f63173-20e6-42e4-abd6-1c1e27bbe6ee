package com.ylkj.controller;

import com.ylkj.common.core.domain.AjaxResult;
import com.ylkj.model.domain.ProductMainImages;
import com.ylkj.model.vo.ProductMainImagesVO;
import com.ylkj.service.IProductMainImagesService;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 商品主图表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
@RestController
@RequestMapping("/front/mainImages")
public class ProductMainImagesController {

    private static final Logger log = LoggerFactory.getLogger(ProductMainImagesController.class);

    @Autowired
    private IProductMainImagesService productMainImagesService;

   
    @GetMapping("/list/sku/{sku}")
    public AjaxResult getByProductSku(@ApiParam("商品SKU") @PathVariable String sku) {
        try {
            List<ProductMainImages> list = productMainImagesService.getByProductSku(sku);
            return AjaxResult.success(list);
        } catch (Exception e) {
            log.error("根据商品SKU获取主图列表失败", e);
            return AjaxResult.error("获取主图列表失败: " + e.getMessage());
        }
    }

    
    @GetMapping("/list/item/{itemId}")
    public AjaxResult getByItemId(@ApiParam("商品ID") @PathVariable String itemId) {
        try {
            List<ProductMainImages> list = productMainImagesService.getByItemId(itemId);
            return AjaxResult.success(list);
        } catch (Exception e) {
            log.error("根据商品ID获取主图列表失败", e);
            return AjaxResult.error("获取主图列表失败: " + e.getMessage());
        }
    }

    
    @GetMapping("/main/sku/{sku}")
    public AjaxResult getMainImageBySku(@ApiParam("商品SKU") @PathVariable String sku) {
        try {
            ProductMainImages mainImage = productMainImagesService.getMainImageBySku(sku);
            return AjaxResult.success(mainImage);
        } catch (Exception e) {
            log.error("根据商品SKU获取主图失败", e);
            return AjaxResult.error("获取主图失败: " + e.getMessage());
        }
    }

   
    @GetMapping("/main/item/{itemId}")
    public AjaxResult getMainImageByItemId(@ApiParam("商品ID") @PathVariable String itemId) {
        try {
            ProductMainImages mainImage = productMainImagesService.getMainImageByItemId(itemId);
            return AjaxResult.success(mainImage);
        } catch (Exception e) {
            log.error("根据商品ID获取主图失败", e);
            return AjaxResult.error("获取主图失败: " + e.getMessage());
        }
    }


    @GetMapping("/vo/list/sku/{sku}")
    public AjaxResult getVOByProductSku(@ApiParam("商品SKU") @PathVariable String sku) {
        try {
            List<ProductMainImagesVO> list = productMainImagesService.getVOByProductSku(sku);
            return AjaxResult.success(list);
        } catch (Exception e) {
            log.error("根据商品SKU获取主图VO列表失败", e);
            return AjaxResult.error("获取主图列表失败: " + e.getMessage());
        }
    }


    @GetMapping("/vo/list/item/{itemId}")
    public AjaxResult getVOByItemId(@ApiParam("商品ID") @PathVariable String itemId) {
        try {
            List<ProductMainImagesVO> list = productMainImagesService.getVOByItemId(itemId);
            return AjaxResult.success(list);
        } catch (Exception e) {
            log.error("根据商品ID获取主图VO列表失败", e);
            return AjaxResult.error("获取主图列表失败: " + e.getMessage());
        }
    }

   
    @PostMapping("/save")
    public AjaxResult save(@RequestBody ProductMainImages productMainImages) {
        try {
            boolean result = productMainImagesService.saveProductMainImage(productMainImages);
            return result ? AjaxResult.success("保存成功") : AjaxResult.error("保存失败");
        } catch (Exception e) {
            log.error("保存商品主图失败", e);
            return AjaxResult.error("保存失败: " + e.getMessage());
        }
    }

 
    @PostMapping("/batch-save")
    public AjaxResult batchSave(@RequestBody List<ProductMainImages> imageList) {
        try {
            boolean result = productMainImagesService.batchSaveProductMainImages(imageList);
            return result ? AjaxResult.success("批量保存成功") : AjaxResult.error("批量保存失败");
        } catch (Exception e) {
            log.error("批量保存商品主图失败", e);
            return AjaxResult.error("批量保存失败: " + e.getMessage());
        }
    }

  
    @PutMapping("/update")
    public AjaxResult update(@RequestBody ProductMainImages productMainImages) {
        try {
            boolean result = productMainImagesService.updateProductMainImage(productMainImages);
            return result ? AjaxResult.success("更新成功") : AjaxResult.error("更新失败");
        } catch (Exception e) {
            log.error("更新商品主图失败", e);
            return AjaxResult.error("更新失败: " + e.getMessage());
        }
    }

   
    @DeleteMapping("/delete/{id}")
    public AjaxResult deleteById(@ApiParam("主图ID") @PathVariable Long id) {
        try {
            boolean result = productMainImagesService.deleteById(id);
            return result ? AjaxResult.success("删除成功") : AjaxResult.error("删除失败");
        } catch (Exception e) {
            log.error("删除商品主图失败", e);
            return AjaxResult.error("删除失败: " + e.getMessage());
        }
    }

   
    @DeleteMapping("/delete/sku/{sku}")
    public AjaxResult deleteByProductSku(@ApiParam("商品SKU") @PathVariable String sku) {
        try {
            boolean result = productMainImagesService.deleteByProductSku(sku);
            return result ? AjaxResult.success("删除成功") : AjaxResult.error("删除失败");
        } catch (Exception e) {
            log.error("根据商品SKU删除主图失败", e);
            return AjaxResult.error("删除失败: " + e.getMessage());
        }
    }

    
    @DeleteMapping("/delete/item/{itemId}")
    public AjaxResult deleteByItemId(@ApiParam("商品ID") @PathVariable String itemId) {
        try {
            boolean result = productMainImagesService.deleteByItemId(itemId);
            return result ? AjaxResult.success("删除成功") : AjaxResult.error("删除失败");
        } catch (Exception e) {
            log.error("根据商品ID删除主图失败", e);
            return AjaxResult.error("删除失败: " + e.getMessage());
        }
    }

   
    @PutMapping("/status/{id}/{status}")
    public AjaxResult updateStatus(@ApiParam("主图ID") @PathVariable Long id, 
                                   @ApiParam("状态") @PathVariable String status) {
        try {
            boolean result = productMainImagesService.updateStatus(id, status);
            return result ? AjaxResult.success("状态更新成功") : AjaxResult.error("状态更新失败");
        } catch (Exception e) {
            log.error("更新主图状态失败", e);
            return AjaxResult.error("状态更新失败: " + e.getMessage());
        }
    }

   
    @PutMapping("/set-main/{sku}/{imageId}")
    public AjaxResult setMainImage(@ApiParam("商品SKU") @PathVariable String sku, 
                                   @ApiParam("图片ID") @PathVariable Long imageId) {
        try {
            boolean result = productMainImagesService.setMainImage(sku, imageId);
            return result ? AjaxResult.success("设置主图成功") : AjaxResult.error("设置主图失败");
        } catch (Exception e) {
            log.error("设置主图标记失败", e);
            return AjaxResult.error("设置主图失败: " + e.getMessage());
        }
    }

   
    @PostMapping("/page")
    public AjaxResult getPageList(@RequestBody ProductMainImages productMainImages,
                                  @RequestParam(defaultValue = "1") int pageNum,
                                  @RequestParam(defaultValue = "10") int pageSize) {
        try {
            PageInfo<ProductMainImages> pageInfo = productMainImagesService.getPageList(productMainImages, pageNum, pageSize);
            return AjaxResult.success(pageInfo);
        } catch (Exception e) {
            log.error("分页查询主图列表失败", e);
            return AjaxResult.error("查询失败: " + e.getMessage());
        }
    }

    
    @PostMapping("/vo/page")
    public AjaxResult getVOPageList(@RequestBody ProductMainImages productMainImages,
                                    @RequestParam(defaultValue = "1") int pageNum,
                                    @RequestParam(defaultValue = "10") int pageSize) {
        try {
            PageInfo<ProductMainImagesVO> pageInfo = productMainImagesService.getVOPageList(productMainImages, pageNum, pageSize);
            return AjaxResult.success(pageInfo);
        } catch (Exception e) {
            log.error("分页查询主图VO列表失败", e);
            return AjaxResult.error("查询失败: " + e.getMessage());
        }
    }

    
    @PostMapping("/list")
    public AjaxResult getByCondition(@RequestBody ProductMainImages productMainImages) {
        try {
            List<ProductMainImages> list = productMainImagesService.getByCondition(productMainImages);
            return AjaxResult.success(list);
        } catch (Exception e) {
            log.error("根据条件查询主图列表失败", e);
            return AjaxResult.error("查询失败: " + e.getMessage());
        }
    }

    
    @PostMapping("/vo/list")
    public AjaxResult getVOByCondition(@RequestBody ProductMainImages productMainImages) {
        try {
            List<ProductMainImagesVO> list = productMainImagesService.getVOByCondition(productMainImages);
            return AjaxResult.success(list);
        } catch (Exception e) {
            log.error("根据条件查询主图VO列表失败", e);
            return AjaxResult.error("查询失败: " + e.getMessage());
        }
    }
}
