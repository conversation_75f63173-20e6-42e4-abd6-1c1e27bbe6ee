package com.ylkj.model.domain.omg;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.Map;

/**
 * @Author: 吴居乐
 * @Description: TODO
 * @DateTime: 2025/7/28/周一 17:13
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BatchProcessResult {
    private int total = 0;
    private int success = 0;
    private int failed = 0;

    /**
     * 存放处理结果
     */
    private Map<String, Object> results = new HashMap<>();

    /**
     * 存放处理错误
     */
    private Map<String, String> errors = new HashMap<>();

    public void incrementSuccess() {
        this.success++;
    }

    public void incrementFailed() {
        this.failed++;
    }

    public void addResult(String itemId, Object result) {
        this.results.put(itemId, result);
    }

    public void addError(String itemId, String error) {
        this.errors.put(itemId, error);
    }
}
