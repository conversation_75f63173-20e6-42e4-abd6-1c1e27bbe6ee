package com.ylkj.service;

import com.ylkj.model.domain.ProductMainImages;
import com.ylkj.model.vo.ProductMainImagesVO;
import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * <p>
 * 商品主图表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
public interface IProductMainImagesService extends IService<ProductMainImages> {

    /**
     * 根据商品SKU获取主图列表
     *
     * @param sku 商品SKU
     * @return 主图列表
     */
    List<ProductMainImages> getByProductSku(String sku);

    /**
     * 根据商品ID获取主图列表
     *
     * @param itemId 商品ID
     * @return 主图列表
     */
    List<ProductMainImages> getByItemId(String itemId);

    /**
     * 根据商品SKU获取主图（仅获取主图标记为1的）
     *
     * @param sku 商品SKU
     * @return 主图信息
     */
    ProductMainImages getMainImageBySku(String sku);

    /**
     * 根据商品ID获取主图（仅获取主图标记为1的）
     *
     * @param itemId 商品ID
     * @return 主图信息
     */
    ProductMainImages getMainImageByItemId(String itemId);

    /**
     * 根据商品SKU获取主图VO列表（包含扩展信息）
     *
     * @param sku 商品SKU
     * @return 主图VO列表
     */
    List<ProductMainImagesVO> getVOByProductSku(String sku);

    /**
     * 根据商品ID获取主图VO列表（包含扩展信息）
     *
     * @param itemId 商品ID
     * @return 主图VO列表
     */
    List<ProductMainImagesVO> getVOByItemId(String itemId);

    /**
     * 保存商品主图
     *
     * @param productMainImages 主图信息
     * @return 是否成功
     */
    boolean saveProductMainImage(ProductMainImages productMainImages);

    /**
     * 批量保存商品主图
     *
     * @param imageList 主图列表
     * @return 是否成功
     */
    boolean batchSaveProductMainImages(List<ProductMainImages> imageList);

    /**
     * 更新商品主图
     *
     * @param productMainImages 主图信息
     * @return 是否成功
     */
    boolean updateProductMainImage(ProductMainImages productMainImages);

    /**
     * 根据ID删除主图
     *
     * @param id 主图ID
     * @return 是否成功
     */
    boolean deleteById(Long id);

    /**
     * 根据商品SKU删除主图
     *
     * @param sku 商品SKU
     * @return 是否成功
     */
    boolean deleteByProductSku(String sku);

    /**
     * 根据商品ID删除主图
     *
     * @param itemId 商品ID
     * @return 是否成功
     */
    boolean deleteByItemId(String itemId);

    /**
     * 更新主图状态
     *
     * @param id 主图ID
     * @param status 状态
     * @return 是否成功
     */
    boolean updateStatus(Long id, String status);

    /**
     * 设置主图标记（将指定图片设为主图，其他设为非主图）
     *
     * @param sku 商品SKU
     * @param imageId 要设为主图的图片ID
     * @return 是否成功
     */
    boolean setMainImage(String sku, Long imageId);

    /**
     * 分页查询主图列表
     *
     * @param productMainImages 查询条件
     * @param pageNum 页码
     * @param pageSize 页大小
     * @return 分页结果
     */
    PageInfo<ProductMainImages> getPageList(ProductMainImages productMainImages, int pageNum, int pageSize);

    /**
     * 分页查询主图VO列表
     *
     * @param productMainImages 查询条件
     * @param pageNum 页码
     * @param pageSize 页大小
     * @return 分页结果
     */
    PageInfo<ProductMainImagesVO> getVOPageList(ProductMainImages productMainImages, int pageNum, int pageSize);

    /**
     * 根据条件查询主图列表
     *
     * @param productMainImages 查询条件
     * @return 主图列表
     */
    List<ProductMainImages> getByCondition(ProductMainImages productMainImages);

    /**
     * 根据条件查询主图VO列表
     *
     * @param productMainImages 查询条件
     * @return 主图VO列表
     */
    List<ProductMainImagesVO> getVOByCondition(ProductMainImages productMainImages);
}
