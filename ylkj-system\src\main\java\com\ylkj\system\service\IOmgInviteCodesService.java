package com.ylkj.system.service;

import java.util.List;
import com.ylkj.system.model.domain.OmgInviteCodes;
import com.ylkj.system.model.vo.omgInviteCodes.OmgInviteCodesVo;
import com.ylkj.system.model.dto.omgInviteCodes.OmgInviteCodesQuery;

import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
/**
 * 邀请码Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
public interface IOmgInviteCodesService extends IService<OmgInviteCodes>
{
    //region mybatis代码
    /**
     * 查询邀请码
     * 
     * @param id 邀请码主键
     * @return 邀请码
     */
    public OmgInviteCodes selectOmgInviteCodesById(Long id);

    /**
     * 查询邀请码列表
     * 
     * @param omgInviteCodes 邀请码
     * @return 邀请码集合
     */
    public List<OmgInviteCodes> selectOmgInviteCodesList(OmgInviteCodes omgInviteCodes);

    /**
     * 新增邀请码
     * 
     * @param omgInviteCodes 邀请码
     * @return 结果
     */
    public int insertOmgInviteCodes(OmgInviteCodes omgInviteCodes);

    /**
     * 修改邀请码
     * 
     * @param omgInviteCodes 邀请码
     * @return 结果
     */
    public int updateOmgInviteCodes(OmgInviteCodes omgInviteCodes);

    /**
     * 批量删除邀请码
     * 
     * @param ids 需要删除的邀请码主键集合
     * @return 结果
     */
    public int deleteOmgInviteCodesByIds(Long[] ids);

    /**
     * 删除邀请码信息
     * 
     * @param id 邀请码主键
     * @return 结果
     */
    public int deleteOmgInviteCodesById(Long id);
    //endregion
    /**
     * 获取查询条件
     *
     * @param omgInviteCodesQuery 查询条件对象
     * @return 查询条件
     */
    QueryWrapper<OmgInviteCodes> getQueryWrapper(OmgInviteCodesQuery omgInviteCodesQuery);

    /**
     * 转换vo
     *
     * @param omgInviteCodesList OmgInviteCodes集合
     * @return OmgInviteCodesVO集合
     */
    List<OmgInviteCodesVo> convertVoList(List<OmgInviteCodes> omgInviteCodesList);
}
