<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ylkj.mapper.OmgInviteRegistrationsMapper">
    
    <resultMap type="com.ylkj.model.domain.OmgInviteRegistrations" id="OmgInviteRegistrationsResult">
        <result property="id"           column="id"            />
        <result property="inviteCode"   column="invite_code"   />
        <result property="userId"       column="user_id"       />
        <result property="registerTime" column="register_time" />
        <result property="registerDate" column="register_date" />
    </resultMap>
    
    <!-- 检查用户是否已通过该邀请码注册过 -->
    <select id="checkUserRegistration" resultType="int">
        SELECT COUNT(*)
        FROM omg_invite_registrations
        WHERE invite_code = #{inviteCode}
          AND user_id = #{userId}
    </select>
    
</mapper>
