package com.ylkj.model.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 邀请码注册记录实体类
 * 
 * <AUTHOR>
 * @date 2025-01-23
 */
@Data
@TableName("omg_invite_registrations")
public class OmgInviteRegistrations implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /** 主键ID */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /** 邀请码 */
    private String inviteCode;
    
    /** 注册用户ID */
    private Long userId;
    
    /** 注册时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date registerTime;

    /** 注册日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date registerDate;
}
