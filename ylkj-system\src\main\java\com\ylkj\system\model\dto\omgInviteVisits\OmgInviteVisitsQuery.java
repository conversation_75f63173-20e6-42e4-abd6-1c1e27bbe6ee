package com.ylkj.system.model.dto.omgInviteVisits;

import java.util.Map;
import java.io.Serializable;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ylkj.system.model.domain.OmgInviteVisit;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonInclude;
import org.springframework.beans.BeanUtils;
import com.baomidou.mybatisplus.annotation.TableField;

/**
 * 邀请码访问记录Query对象 omg_invite_visits
 *
 * <AUTHOR>
 * @date 2025-07-23
 */
@Data
public class OmgInviteVisitsQuery implements Serializable
{
    private static final long serialVersionUID = 1L;
    private Long id;
    /** 邀请码 */
    private String inviteCode;

    /** 访问者IP */
    private String visitorIp;

    /** 访问时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date visitTime;

    /** 访问日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date visitDate;

    /** 请求参数 */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @TableField(exist = false)
    private Map<String, Object> params;

    /**
     * 对象转封装类
     *
     * @param omgInviteVisitsQuery 查询对象
     * @return OmgInviteVisit
     */
    public static OmgInviteVisit queryToObj(OmgInviteVisitsQuery omgInviteVisitsQuery) {
        if (omgInviteVisitsQuery == null) {
            return null;
        }
        OmgInviteVisit omgInviteVisit = new OmgInviteVisit();
        BeanUtils.copyProperties(omgInviteVisitsQuery, omgInviteVisit);
        return omgInviteVisit;
    }
}
