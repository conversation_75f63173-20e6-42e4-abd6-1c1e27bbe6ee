package com.ylkj.model.domain.omg;

import com.ylkj.service.FindQcApiService;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Author: 吴居乐
 * @Description: FindQC图片处理结果类
 * @DateTime: 2025/7/28/周一 17:06
 * @Version: 1.0
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class FindQcImageProcessResult {
    /**
     * 处理结果是否成功
     */
    private boolean success;
    /**
     * 处理结果信息
     */
    private String message;
    /**
     * 原始图片列表
     */
    private FindQcApiService.FindQcProductInfo productInfo;
    /**
     * 原始图片数量
     */
    private List<String> picListOssUrls;
    /**
     * 处理后的图片列表
     */
    private List<String> qcListOssUrls;

    /**
     * 原始图片数量
     */
    private int totalOriginalImages;

    /**
     * 处理后的图片数量
     */
    private int totalUploadedImages;
}
