package com.ylkj.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

/**
 * 邀请码客户端Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-23
 */
@Mapper
public interface OmgInviteCodesClientMapper {

    /**
     * 验证邀请码是否有效
     *
     * @param inviteCode 邀请码
     * @return 记录数量
     */
    @Select("SELECT COUNT(*) FROM omg_invite_codes WHERE invite_code = #{inviteCode} AND status = 'active'")
    int validateInviteCode(@Param("inviteCode") String inviteCode);

    /**
     * 实时查询邀请码总访问量
     *
     * @param inviteCode 邀请码
     * @return 总访问量
     */
    @Select("SELECT COUNT(*) FROM omg_invite_visits WHERE invite_code = #{inviteCode}")
    int getTotalVisits(@Param("inviteCode") String inviteCode);

    /**
     * 实时查询邀请码总注册数
     *
     * @param inviteCode 邀请码
     * @return 总注册数
     */
    @Select("SELECT COUNT(*) FROM omg_invite_registrations WHERE invite_code = #{inviteCode}")
    int getTotalRegistrations(@Param("inviteCode") String inviteCode);

    /**
     * 更新邀请码访问量统计
     *
     * @param inviteCode 邀请码
     * @param totalVisits 总访问量
     * @return 影响行数
     */
    @Update("UPDATE omg_invite_codes SET total_visits = #{totalVisits} WHERE invite_code = #{inviteCode} AND status = 'active'")
    int updateVisitsStats(@Param("inviteCode") String inviteCode, @Param("totalVisits") int totalVisits);

    /**
     * 更新邀请码注册数统计
     *
     * @param inviteCode 邀请码
     * @param totalRegistrations 总注册数
     * @return 影响行数
     */
    @Update("UPDATE omg_invite_codes SET total_registrations = #{totalRegistrations} WHERE invite_code = #{inviteCode} AND status = 'active'")
    int updateRegistrationsStats(@Param("inviteCode") String inviteCode, @Param("totalRegistrations") int totalRegistrations);
}
