package com.ylkj.system.mapper;

import java.util.List;
import com.ylkj.system.model.domain.OmgInviteRegistration;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * 邀请码注册记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
public interface OmgInviteRegistrationMapper extends BaseMapper<OmgInviteRegistration>
{
    /**
     * 查询邀请码注册记录
     * 
     * @param id 邀请码注册记录主键
     * @return 邀请码注册记录
     */
    public OmgInviteRegistration selectOmgInviteRegistrationById(Long id);

    /**
     * 查询邀请码注册记录列表
     * 
     * @param omgInviteRegistration 邀请码注册记录
     * @return 邀请码注册记录集合
     */
    public List<OmgInviteRegistration> selectOmgInviteRegistrationList(OmgInviteRegistration omgInviteRegistration);

    /**
     * 新增邀请码注册记录
     * 
     * @param omgInviteRegistration 邀请码注册记录
     * @return 结果
     */
    public int insertOmgInviteRegistration(OmgInviteRegistration omgInviteRegistration);

    /**
     * 修改邀请码注册记录
     * 
     * @param omgInviteRegistration 邀请码注册记录
     * @return 结果
     */
    public int updateOmgInviteRegistration(OmgInviteRegistration omgInviteRegistration);

    /**
     * 删除邀请码注册记录
     * 
     * @param id 邀请码注册记录主键
     * @return 结果
     */
    public int deleteOmgInviteRegistrationById(Long id);

    /**
     * 批量删除邀请码注册记录
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteOmgInviteRegistrationByIds(Long[] ids);
}
