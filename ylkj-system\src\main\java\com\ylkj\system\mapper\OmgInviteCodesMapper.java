package com.ylkj.system.mapper;

import java.util.List;
import com.ylkj.system.model.domain.OmgInviteCodes;
import com.ylkj.system.model.dto.inviteStats.InviteTimeStatsDto;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * 邀请码Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
public interface OmgInviteCodesMapper extends BaseMapper<OmgInviteCodes>
{
    /**
     * 查询邀请码
     * 
     * @param id 邀请码主键
     * @return 邀请码
     */
    public OmgInviteCodes selectOmgInviteCodesById(Long id);

    /**
     * 查询邀请码列表
     * 
     * @param omgInviteCodes 邀请码
     * @return 邀请码集合
     */
    public List<OmgInviteCodes> selectOmgInviteCodesList(OmgInviteCodes omgInviteCodes);

    /**
     * 新增邀请码
     * 
     * @param omgInviteCodes 邀请码
     * @return 结果
     */
    public int insertOmgInviteCodes(OmgInviteCodes omgInviteCodes);

    /**
     * 修改邀请码
     * 
     * @param omgInviteCodes 邀请码
     * @return 结果
     */
    public int updateOmgInviteCodes(OmgInviteCodes omgInviteCodes);

    /**
     * 删除邀请码
     * 
     * @param id 邀请码主键
     * @return 结果
     */
    public int deleteOmgInviteCodesById(Long id);

    /**
     * 批量删除邀请码
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteOmgInviteCodesByIds(Long[] ids);

    /**
     * 按天统计邀请码数据
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param inviteCode 邀请码（可选）
     * @return 每日统计数据
     */
    public List<InviteTimeStatsDto> selectDailyStats(@Param("startDate") String startDate,
                                                    @Param("endDate") String endDate,
                                                    @Param("inviteCode") String inviteCode);

    /**
     * 按周统计邀请码数据
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param inviteCode 邀请码（可选）
     * @return 每周统计数据
     */
    public List<InviteTimeStatsDto> selectWeeklyStats(@Param("startDate") String startDate,
                                                     @Param("endDate") String endDate,
                                                     @Param("inviteCode") String inviteCode);

    /**
     * 按月统计邀请码数据
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param inviteCode 邀请码（可选）
     * @return 每月统计数据
     */
    public List<InviteTimeStatsDto> selectMonthlyStats(@Param("startDate") String startDate,
                                                      @Param("endDate") String endDate,
                                                      @Param("inviteCode") String inviteCode);

}
