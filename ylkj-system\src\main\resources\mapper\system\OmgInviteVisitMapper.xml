<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ylkj.system.mapper.OmgInviteVisitMapper">
    
    <resultMap type="OmgInviteVisit" id="OmgInviteVisitResult">
        <result property="id"    column="id"    />
        <result property="inviteCode"    column="invite_code"    />
        <result property="visitorIp"    column="visitor_ip"    />
        <result property="visitTime"    column="visit_time"    />
        <result property="visitDate"    column="visit_date"    />
    </resultMap>

    <sql id="selectOmgInviteVisitVo">
        select id, invite_code, visitor_ip, visit_time, visit_date from omg_invite_visits
    </sql>

    <select id="selectOmgInviteVisitsList" parameterType="OmgInviteVisit" resultMap="OmgInviteVisitResult">
        <include refid="selectOmgInviteVisitVo"/>
        <where>
            <if test="inviteCode != null  and inviteCode != ''"> and invite_code = #{inviteCode}</if>
            <if test="visitorIp != null  and visitorIp != ''"> and visitor_ip = #{visitorIp}</if>
            <if test="visitTime != null "> and visit_time = #{visitTime}</if>
            <if test="visitDate != null "> and visit_date = #{visitDate}</if>
        </where>
    </select>

    <select id="selectOmgInviteVisitsById" parameterType="Long" resultMap="OmgInviteVisitResult">
        <include refid="selectOmgInviteVisitVo"/>
        where id = #{id}
    </select>

    <insert id="insertOmgInviteVisits" parameterType="OmgInviteVisit" useGeneratedKeys="true" keyProperty="id">
        insert into omg_invite_visits
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="inviteCode != null and inviteCode != ''">invite_code,</if>
            <if test="visitorIp != null">visitor_ip,</if>
            <if test="visitTime != null">visit_time,</if>
            <if test="visitDate != null">visit_date,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="inviteCode != null and inviteCode != ''">#{inviteCode},</if>
            <if test="visitorIp != null">#{visitorIp},</if>
            <if test="visitTime != null">#{visitTime},</if>
            <if test="visitDate != null">#{visitDate},</if>
         </trim>
    </insert>

    <update id="updateOmgInviteVisits" parameterType="OmgInviteVisit">
        update omg_invite_visits
        <trim prefix="SET" suffixOverrides=",">
            <if test="inviteCode != null and inviteCode != ''">invite_code = #{inviteCode},</if>
            <if test="visitorIp != null">visitor_ip = #{visitorIp},</if>
            <if test="visitTime != null">visit_time = #{visitTime},</if>
            <if test="visitDate != null">visit_date = #{visitDate},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteOmgInviteVisitsById" parameterType="Long">
        delete from omg_invite_visits where id = #{id}
    </delete>

    <delete id="deleteOmgInviteVisitsByIds" parameterType="String">
        delete from omg_invite_visits where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>