package com.ylkj.model.domain.omg;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Author: 吴居乐
 * @Description: TODO
 * @DateTime: 2025/7/28/周一 17:12
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BatchProcessRequest {

    /**
     * 币种
     */
    private String currencyType = "USD";

    /**
     * 语言
     */
    private String langType = "en";

    /**
     * 处理项
     */
    private List<ProcessItem> items;


}
