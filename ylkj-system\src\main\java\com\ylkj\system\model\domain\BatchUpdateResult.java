package com.ylkj.system.model.domain;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.Map;

/**
 * @Author: 吴居乐
 * @Description: TODO
 * @DateTime: 2025/7/28/周一 18:19
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BatchUpdateResult {

    /**
     * 总数
     */
    private int total = 0;
    /**
     * 成功数
     */
    private int success = 0;
    /**
     * 失败数
     */
    private int failed = 0;
    /**
     * 未支持数
     */
    private int skipped = 0;
    /**
     * 失败的sku
     */
    private Map<String, String> successSkus = new HashMap<>();
    /**
     * 未支持的sku
     */
    private Map<String, String> failedSkus = new HashMap<>();
    /**
     * 跳过的sku
     */
    private Map<String, String> skippedSkus = new HashMap<>();

    public void incrementSuccess() { this.success++; }
    public void incrementFailed() { this.failed++; }
    public void incrementSkipped() { this.skipped++; }

    public void addSuccessSku(String sku, String mainImageUrl) {
        this.successSkus.put(sku, mainImageUrl);
    }
    public void addFailedSku(String sku, String reason) {
        this.failedSkus.put(sku, reason);
    }
    public void addSkippedSku(String sku, String reason) {
        this.skippedSkus.put(sku, reason);
    }
}
