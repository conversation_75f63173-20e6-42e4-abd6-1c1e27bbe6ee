package com.ylkj.system.model.domain;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.Map;

/**
 * @Author: 吴居乐
 * @Description: 主图更新统计类
 * @DateTime: 2025/7/28/周一 18:17
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MainImageUpdateStats {

    /**
     * 总商品数
     */
    private int totalProducts = 0;
    /**
     * 需要更新商品数
     */
    private int needUpdate = 0;
    /**
     * 已有商品数
     */
    private int alreadyHave = 0;
    /**
     * 平台统计
     */
    private Map<String, Integer> platformStats = new HashMap<>();
    /**
     * 需要更新商品数
     */
    private Map<String, Integer> needUpdateStats = new HashMap<>();

    public void incrementNeedUpdate() { this.needUpdate++; }
    public void incrementAlreadyHave() { this.alreadyHave++; }
}
