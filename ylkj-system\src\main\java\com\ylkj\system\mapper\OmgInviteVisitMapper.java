package com.ylkj.system.mapper;

import java.util.List;

import com.ylkj.system.model.domain.OmgInviteVisit;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * 邀请码访问记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
public interface OmgInviteVisitMapper extends BaseMapper<OmgInviteVisit>
{
    /**
     * 查询邀请码访问记录
     * 
     * @param id 邀请码访问记录主键
     * @return 邀请码访问记录
     */
    public OmgInviteVisit selectOmgInviteVisitsById(Long id);

    /**
     * 查询邀请码访问记录列表
     * 
     * @param omgInviteVisit 邀请码访问记录
     * @return 邀请码访问记录集合
     */
    public List<OmgInviteVisit> selectOmgInviteVisitsList(OmgInviteVisit omgInviteVisit);

    /**
     * 新增邀请码访问记录
     * 
     * @param omgInviteVisit 邀请码访问记录
     * @return 结果
     */
    public int insertOmgInviteVisits(OmgInviteVisit omgInviteVisit);

    /**
     * 修改邀请码访问记录
     * 
     * @param omgInviteVisit 邀请码访问记录
     * @return 结果
     */
    public int updateOmgInviteVisits(OmgInviteVisit omgInviteVisit);

    /**
     * 删除邀请码访问记录
     * 
     * @param id 邀请码访问记录主键
     * @return 结果
     */
    public int deleteOmgInviteVisitsById(Long id);

    /**
     * 批量删除邀请码访问记录
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteOmgInviteVisitsByIds(Long[] ids);
}
