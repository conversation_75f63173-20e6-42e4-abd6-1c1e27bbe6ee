package com.ylkj.system.service.impl;

import com.ylkj.system.mapper.OmgInviteCodesMapper;
import com.ylkj.system.model.dto.inviteStats.InviteTimeStatsDto;
import com.ylkj.system.service.IInviteTimeStatsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 邀请码时间维度统计Service实现类
 * 
 * <AUTHOR>
 * @date 2025-01-23
 */
@Service
public class InviteTimeStatsServiceImpl implements IInviteTimeStatsService {
    
    @Autowired
    private OmgInviteCodesMapper inviteCodesMapper;
    
    @Override
    public List<InviteTimeStatsDto> getDailyStats(String startDate, String endDate, String inviteCode) {
        return inviteCodesMapper.selectDailyStats(startDate, endDate, inviteCode);
    }
    
    @Override
    public List<InviteTimeStatsDto> getWeeklyStats(String startDate, String endDate, String inviteCode) {
        return inviteCodesMapper.selectWeeklyStats(startDate, endDate, inviteCode);
    }
    
    @Override
    public List<InviteTimeStatsDto> getMonthlyStats(String startDate, String endDate, String inviteCode) {
        return inviteCodesMapper.selectMonthlyStats(startDate, endDate, inviteCode);
    }
}
