package com.ylkj.system.model.vo.omgInviteRegistration;

import java.io.Serializable;
import java.util.Date;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import com.ylkj.common.annotation.Excel;
import org.springframework.beans.BeanUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ylkj.system.model.domain.OmgInviteRegistration;
/**
 * 邀请码注册记录Vo对象 omg_invite_registrations
 *
 * <AUTHOR>
 * @date 2025-07-23
 */
@Data
public class OmgInviteRegistrationVo implements Serializable
{
    private static final long serialVersionUID = 1L;
    private Long id;
    /** 邀请码 */
    @Excel(name = "邀请码")
    private String inviteCode;

    /** 注册的用户ID */
    @Excel(name = "注册的用户ID")
    private Long userId;

    /** 注册时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "注册时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date registerTime;

    /** 注册日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "注册日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date registerDate;


     /**
     * 对象转封装类
     *
     * @param omgInviteRegistration OmgInviteRegistration实体对象
     * @return OmgInviteRegistrationVo
     */
    public static OmgInviteRegistrationVo objToVo(OmgInviteRegistration omgInviteRegistration) {
        if (omgInviteRegistration == null) {
            return null;
        }
        OmgInviteRegistrationVo omgInviteRegistrationVo = new OmgInviteRegistrationVo();
        BeanUtils.copyProperties(omgInviteRegistration, omgInviteRegistrationVo);
        return omgInviteRegistrationVo;
    }
}
