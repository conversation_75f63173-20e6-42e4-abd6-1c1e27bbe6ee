# OMG商品主图管理API文档

## 概述
本文档描述了OMG商品主图管理相关的API接口，包括设置主图、取消主图、批量操作等功能。

## 基础信息
- **基础路径**: `/system/OmgProducts`
- **认证方式**: 需要相应的权限认证
- **数据格式**: JSON

## API接口列表

### 1. 设置商品主图

**接口地址**: `POST /system/OmgProducts/setMainImage`

**功能描述**: 将指定的图片设置为商品主图

**权限要求**: `system:OmgProducts:edit`

**请求参数**:
```json
{
  "imageId": 123,
  "operator": "admin",
  "reason": "设置主图原因",
  "forceOperation": false
}
```

**参数说明**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| imageId | Long | 是 | 图片ID |
| operator | String | 否 | 操作者 |
| reason | String | 否 | 操作原因 |
| forceOperation | Boolean | 否 | 是否强制操作 |

**响应示例**:
```json
{
  "code": 200,
  "msg": "设置主图成功",
  "data": null
}
```

### 2. 取消商品主图

**接口地址**: `POST /system/OmgProducts/cancelMainImage`

**功能描述**: 取消指定图片的主图标记

**权限要求**: `system:OmgProducts:edit`

**请求参数**:
```json
{
  "imageId": 123,
  "operator": "admin",
  "reason": "取消主图原因",
  "forceOperation": false
}
```

**参数说明**: 同设置主图接口

**响应示例**:
```json
{
  "code": 200,
  "msg": "取消主图成功",
  "data": null
}
```

### 3. 批量设置主图

**接口地址**: `POST /system/OmgProducts/batchSetMainImage`

**功能描述**: 批量设置多个图片为各自商品的主图

**权限要求**: `system:OmgProducts:edit`

**请求参数**:
```json
{
  "imageIds": [123, 124, 125],
  "operator": "admin",
  "reason": "批量设置主图"
}
```

**参数说明**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| imageIds | List<Long> | 是 | 图片ID列表 |
| operator | String | 否 | 操作者 |
| reason | String | 否 | 操作原因 |

**响应示例**:
```json
{
  "code": 200,
  "msg": "批量设置主图完成，成功: 3, 失败: 0",
  "data": null
}
```

### 4. 获取商品主图列表

**接口地址**: `GET /system/OmgProducts/mainImages/{sku}`

**功能描述**: 根据SKU获取商品的所有主图

**权限要求**: `system:OmgProducts:query`

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| sku | String | 是 | 商品SKU |

**响应示例**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "id": 123,
      "sku": "TEST001",
      "itemId": "ITEM001",
      "mallType": "T1688",
      "originalImageUrl": "https://example.com/original.jpg",
      "ossImageUrl": "https://oss.example.com/image.jpg",
      "imageName": "image.jpg",
      "imageSize": 102400,
      "imageType": "jpg",
      "displayOrder": 1,
      "isMain": 1,
      "status": "active",
      "source": "API",
      "createTime": "2025-07-28 10:00:00",
      "updateTime": "2025-07-28 10:00:00"
    }
  ]
}
```

### 5. 获取当前主图

**接口地址**: `GET /system/OmgProducts/currentMainImage/{sku}`

**功能描述**: 根据SKU获取商品的当前主图

**权限要求**: `system:OmgProducts:query`

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| sku | String | 是 | 商品SKU |

**响应示例**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "id": 123,
    "sku": "TEST001",
    "isMain": 1,
    "ossImageUrl": "https://oss.example.com/main-image.jpg",
    // ... 其他字段
  }
}
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 操作成功 |
| 500 | 操作失败 |

## 常见错误信息

| 错误信息 | 原因 | 解决方案 |
|----------|------|----------|
| 参数错误：图片ID不能为空 | 请求参数中imageId为空 | 检查请求参数 |
| 图片不存在 | 指定的图片ID在数据库中不存在 | 确认图片ID是否正确 |
| 图片状态异常，无法设置为主图 | 图片状态不是active | 检查图片状态 |
| 该图片不是主图，无需取消 | 尝试取消非主图的主图标记 | 确认图片是否为主图 |

## 使用示例

### JavaScript示例

```javascript
// 设置主图
const setMainImage = async (imageId) => {
  const response = await fetch('/system/OmgProducts/setMainImage', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer ' + token
    },
    body: JSON.stringify({
      imageId: imageId,
      operator: 'admin'
    })
  });
  
  const result = await response.json();
  if (result.code === 200) {
    console.log('设置主图成功');
  } else {
    console.error('设置主图失败:', result.msg);
  }
};

// 取消主图
const cancelMainImage = async (imageId) => {
  const response = await fetch('/system/OmgProducts/cancelMainImage', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer ' + token
    },
    body: JSON.stringify({
      imageId: imageId,
      operator: 'admin'
    })
  });
  
  const result = await response.json();
  if (result.code === 200) {
    console.log('取消主图成功');
  } else {
    console.error('取消主图失败:', result.msg);
  }
};

// 获取主图列表
const getMainImages = async (sku) => {
  const response = await fetch(`/system/OmgProducts/mainImages/${sku}`, {
    method: 'GET',
    headers: {
      'Authorization': 'Bearer ' + token
    }
  });
  
  const result = await response.json();
  if (result.code === 200) {
    console.log('主图列表:', result.data);
    return result.data;
  } else {
    console.error('获取主图列表失败:', result.msg);
    return [];
  }
};
```

## 注意事项

1. **权限控制**: 所有接口都需要相应的权限认证
2. **数据一致性**: 设置主图时会自动清除该SKU的其他主图标记
3. **状态检查**: 只有状态为"active"的图片才能设置为主图
4. **批量操作**: 批量操作会返回详细的成功/失败统计信息
5. **日志记录**: 所有操作都会记录详细的日志信息
6. **异常处理**: 接口具有完善的异常处理机制，会返回具体的错误信息
