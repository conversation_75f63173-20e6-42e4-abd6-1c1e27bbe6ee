# 主图排序功能说明

## 功能概述
已优化所有主图查询接口，确保 `is_main=1` 的主图始终排在列表的第一位。

## 修改的查询接口

### 1. 客户端模块 (ylkj-client)

**文件**: `ylkj-client/src/main/resources/mapper/ProductMainImagesMapper.xml`

#### 修改的查询方法：

1. **selectByProductSku** - 根据商品SKU获取主图列表
```sql
ORDER BY is_main DESC, display_order ASC, create_time DESC
```

2. **selectByItemId** - 根据商品ID获取主图列表
```sql
ORDER BY is_main DESC, display_order ASC, create_time DESC
```

3. **selectVOByProductSku** - 根据商品SKU获取主图VO列表
```sql
ORDER BY pmi.is_main DESC, pmi.display_order ASC, pmi.create_time DESC
```

4. **selectVOByItemId** - 根据商品ID获取主图VO列表
```sql
ORDER BY pmi.is_main DESC, pmi.display_order ASC, pmi.create_time DESC
```

5. **selectVOByCondition** - 根据条件查询主图VO列表
```sql
ORDER BY pmi.is_main DESC, pmi.display_order ASC, pmi.create_time DESC
```

### 2. 系统模块 (ylkj-system)

**文件**: `ylkj-system/src/main/resources/mapper/system/OmgProductMainImagesMapper.xml`

#### 修改的查询方法：

1. **selectImagesBySku** - 根据SKU查询商品主图列表
```sql
order by is_main desc, display_order, create_time desc
```

2. **selectImagesBySkuAndSource** - 根据SKU和来源查询图片列表
```sql
order by is_main desc, display_order, create_time desc
```

## 排序规则说明

### 排序优先级（从高到低）：
1. **is_main DESC** - 主图标记降序（1在前，0在后）
2. **display_order ASC** - 显示顺序升序（小数字在前）
3. **create_time DESC** - 创建时间降序（新的在前）

### 排序效果：
- `is_main=1` 的图片（主图）始终排在最前面
- 如果有多个主图（理论上不应该存在），按显示顺序排序
- 非主图按显示顺序和创建时间排序

## 数据示例

假设有以下数据：
```
id | sku     | is_main | display_order | create_time
1  | TEST001 | 0       | 1            | 2025-07-28 10:00:00
2  | TEST001 | 1       | 3            | 2025-07-28 11:00:00  <- 主图
3  | TEST001 | 0       | 2            | 2025-07-28 12:00:00
4  | TEST001 | 0       | 1            | 2025-07-28 13:00:00
```

**排序后的结果**：
```
1. id=2 (is_main=1, display_order=3) <- 主图排第一
2. id=1 (is_main=0, display_order=1, 较早创建)
3. id=4 (is_main=0, display_order=1, 较晚创建)
4. id=3 (is_main=0, display_order=2)
```

## 影响的API接口

### 客户端API：
- `GET /front/mainImages/list/sku/{sku}` - 获取主图列表
- `GET /front/mainImages/list/item/{itemId}` - 获取主图列表
- `GET /front/mainImages/vo/list/sku/{sku}` - 获取主图VO列表
- `GET /front/mainImages/vo/list/item/{itemId}` - 获取主图VO列表
- `POST /front/mainImages/vo/list` - 条件查询主图VO列表

### 管理端API：
- `GET /system/OmgProducts/mainImages/{sku}` - 获取商品主图列表

## 测试建议

### 1. 单元测试
```java
@Test
public void testMainImageSorting() {
    String sku = "TEST001";
    List<ProductMainImages> images = productMainImagesService.getByProductSku(sku);
    
    // 验证第一张图片是主图
    if (!images.isEmpty()) {
        ProductMainImages firstImage = images.get(0);
        assertEquals(1, firstImage.getIsMain().intValue(), "第一张图片应该是主图");
    }
    
    // 验证排序正确性
    for (int i = 0; i < images.size() - 1; i++) {
        ProductMainImages current = images.get(i);
        ProductMainImages next = images.get(i + 1);
        
        // 主图应该在非主图前面
        if (current.getIsMain() == 0 && next.getIsMain() == 1) {
            fail("主图应该排在非主图前面");
        }
    }
}
```

### 2. 集成测试
```java
@Test
public void testApiMainImageSorting() {
    // 调用API获取主图列表
    String sku = "TEST001";
    AjaxResult result = controller.getByProductSku(sku);
    
    List<ProductMainImages> images = (List<ProductMainImages>) result.get("data");
    
    // 验证主图排序
    if (!images.isEmpty()) {
        assertTrue(images.get(0).getIsMain() == 1, "API返回的第一张图片应该是主图");
    }
}
```

### 3. 数据库验证
```sql
-- 验证排序SQL
SELECT id, sku, is_main, display_order, create_time
FROM omg_product_main_images 
WHERE sku = 'TEST001' AND status = 'active'
ORDER BY is_main DESC, display_order ASC, create_time DESC;
```

## 注意事项

1. **数据一致性**：确保每个SKU只有一个主图（is_main=1）
2. **性能考虑**：is_main字段已建立索引，排序性能良好
3. **向后兼容**：修改只影响排序，不影响数据结构
4. **缓存更新**：如果使用了缓存，需要清除相关缓存

## 相关文件清单

### 修改的文件：
1. `ylkj-client/src/main/resources/mapper/ProductMainImagesMapper.xml`
2. `ylkj-system/src/main/resources/mapper/system/OmgProductMainImagesMapper.xml`

### 相关接口：
1. `ProductMainImagesMapper.java` - 客户端Mapper接口
2. `OmgProductMainImagesMapper.java` - 系统模块Mapper接口
3. `ProductMainImagesController.java` - 客户端控制器
4. `OmgProductsController.java` - 管理端控制器

这个修改确保了所有获取主图列表的查询都会将主图（is_main=1）排在第一位，满足前端显示需求。
