-- ----------------------------
-- 客户端商品主图表
-- ----------------------------
DROP TABLE IF EXISTS `product_main_images`;
CREATE TABLE `product_main_images` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `sku` varchar(100) DEFAULT NULL COMMENT '商品SKU',
  `item_id` varchar(100) DEFAULT NULL COMMENT '商品ID',
  `mall_type` varchar(50) DEFAULT NULL COMMENT '商城类型',
  `original_image_url` varchar(500) DEFAULT NULL COMMENT '原始图片URL',
  `oss_image_url` varchar(500) DEFAULT NULL COMMENT 'OSS图片URL',
  `image_name` varchar(200) DEFAULT NULL COMMENT '图片文件名',
  `image_size` bigint(20) DEFAULT NULL COMMENT '图片大小(字节)',
  `image_type` varchar(50) DEFAULT NULL COMMENT '图片类型',
  `display_order` int(11) DEFAULT '0' COMMENT '显示顺序',
  `is_main` tinyint(1) DEFAULT '0' COMMENT '是否为主图(0-否,1-是)',
  `status` char(1) DEFAULT '1' COMMENT '状态(0-禁用,1-启用)',
  `source` varchar(100) DEFAULT NULL COMMENT '图片来源',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  PRIMARY KEY (`id`),
  KEY `idx_sku` (`sku`),
  KEY `idx_item_id` (`item_id`),
  KEY `idx_mall_type` (`mall_type`),
  KEY `idx_is_main` (`is_main`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_sku_main` (`sku`, `is_main`),
  KEY `idx_item_id_main` (`item_id`, `is_main`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='客户端商品主图表';

-- ----------------------------
-- 初始化数据（示例）
-- ----------------------------
INSERT INTO `product_main_images` VALUES 
(1, 'TEST001', 'ITEM001', 'T1688', 'https://example.com/original/image1.jpg', 'https://oss.example.com/image1.jpg', 'image1.jpg', 102400, 'jpg', 1, 1, '1', 'API', '测试主图1', NOW(), NOW(), 'system', 'system'),
(2, 'TEST001', 'ITEM001', 'T1688', 'https://example.com/original/image2.jpg', 'https://oss.example.com/image2.jpg', 'image2.jpg', 204800, 'jpg', 2, 0, '1', 'API', '测试主图2', NOW(), NOW(), 'system', 'system'),
(3, 'TEST002', 'ITEM002', 'TAOBAO', 'https://example.com/original/image3.jpg', 'https://oss.example.com/image3.jpg', 'image3.jpg', 153600, 'png', 1, 1, '1', 'UPLOAD', '测试主图3', NOW(), NOW(), 'system', 'system');

-- ----------------------------
-- 添加注释
-- ----------------------------
ALTER TABLE `product_main_images` COMMENT = '客户端商品主图表，用于存储商品的主图信息，支持多图片管理和主图标记';

-- ----------------------------
-- 创建视图（可选）
-- ----------------------------
CREATE OR REPLACE VIEW `v_product_main_images` AS
SELECT 
    pmi.*,
    p.name as product_name,
    p.price as product_price,
    CASE 
        WHEN pmi.is_main = 1 THEN '主图'
        ELSE '副图'
    END as image_type_desc,
    CASE 
        WHEN pmi.status = '1' THEN '启用'
        ELSE '禁用'
    END as status_desc
FROM product_main_images pmi
LEFT JOIN products p ON pmi.sku = p.sku OR pmi.item_id = p.item_id
WHERE pmi.status = '1'
ORDER BY pmi.sku, pmi.display_order ASC, pmi.create_time DESC;
