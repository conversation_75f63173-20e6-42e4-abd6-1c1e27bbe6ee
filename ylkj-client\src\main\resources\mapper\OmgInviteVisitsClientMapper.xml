<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ylkj.mapper.OmgInviteVisitsClientMapper">
    
    <resultMap type="com.ylkj.model.domain.OmgInviteVisits" id="OmgInviteVisitsResult">
        <result property="id"          column="id"          />
        <result property="inviteCode"  column="invite_code" />
        <result property="visitorIp"   column="visitor_ip"  />
        <result property="visitTime"   column="visit_time"  />
        <result property="visitDate"   column="visit_date"  />
    </resultMap>
    
    <!-- 检查是否存在重复访问记录（防刷） -->
    <select id="checkDuplicateVisit" resultType="int">
        SELECT COUNT(*)
        FROM omg_invite_visits
        WHERE invite_code = #{inviteCode}
          AND visitor_ip = #{visitorIp}
          AND visit_time > DATE_SUB(NOW(), INTERVAL #{minutes} MINUTE)
    </select>
    
</mapper>
