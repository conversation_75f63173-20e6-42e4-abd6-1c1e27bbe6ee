package com.ylkj.service;

import com.ylkj.common.core.domain.AjaxResult;
import java.util.Map;

/**
 * 邀请码客户端服务接口
 *
 * <AUTHOR>
 * @date 2025-01-23
 */
public interface InviteCodeClientService {
    
    /**
     * 记录邀请码访问
     *
     * @param inviteCode 邀请码
     * @param visitorIp 访问者IP
     * @return 操作结果
     */
    AjaxResult recordVisit(String inviteCode, String visitorIp);

    /**
     * 记录邀请码注册
     *
     * @param requestBody 包含邀请码和用户ID的请求数据
     * @return 操作结果
     */
    AjaxResult recordRegistration(Map<String, Object> requestBody);

    /**
     * 验证邀请码是否有效
     *
     * @param inviteCode 邀请码
     * @return 验证结果
     */
    AjaxResult validateInviteCode(String inviteCode);
}
