package com.ylkj.system.service;

import java.util.List;
import com.ylkj.system.model.domain.OmgInviteRegistration;
import com.ylkj.system.model.vo.omgInviteRegistration.OmgInviteRegistrationVo;
import com.ylkj.system.model.dto.omgInviteRegistration.OmgInviteRegistrationQuery;

import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
/**
 * 邀请码注册记录Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
public interface IOmgInviteRegistrationService extends IService<OmgInviteRegistration>
{
    //region mybatis代码
    /**
     * 查询邀请码注册记录
     * 
     * @param id 邀请码注册记录主键
     * @return 邀请码注册记录
     */
    public OmgInviteRegistration selectOmgInviteRegistrationById(Long id);

    /**
     * 查询邀请码注册记录列表
     * 
     * @param omgInviteRegistration 邀请码注册记录
     * @return 邀请码注册记录集合
     */
    public List<OmgInviteRegistration> selectOmgInviteRegistrationList(OmgInviteRegistration omgInviteRegistration);

    /**
     * 新增邀请码注册记录
     * 
     * @param omgInviteRegistration 邀请码注册记录
     * @return 结果
     */
    public int insertOmgInviteRegistration(OmgInviteRegistration omgInviteRegistration);

    /**
     * 修改邀请码注册记录
     * 
     * @param omgInviteRegistration 邀请码注册记录
     * @return 结果
     */
    public int updateOmgInviteRegistration(OmgInviteRegistration omgInviteRegistration);

    /**
     * 批量删除邀请码注册记录
     * 
     * @param ids 需要删除的邀请码注册记录主键集合
     * @return 结果
     */
    public int deleteOmgInviteRegistrationByIds(Long[] ids);

    /**
     * 删除邀请码注册记录信息
     * 
     * @param id 邀请码注册记录主键
     * @return 结果
     */
    public int deleteOmgInviteRegistrationById(Long id);
    //endregion
    /**
     * 获取查询条件
     *
     * @param omgInviteRegistrationQuery 查询条件对象
     * @return 查询条件
     */
    QueryWrapper<OmgInviteRegistration> getQueryWrapper(OmgInviteRegistrationQuery omgInviteRegistrationQuery);

    /**
     * 转换vo
     *
     * @param omgInviteRegistrationList OmgInviteRegistration集合
     * @return OmgInviteRegistrationVO集合
     */
    List<OmgInviteRegistrationVo> convertVoList(List<OmgInviteRegistration> omgInviteRegistrationList);
}
