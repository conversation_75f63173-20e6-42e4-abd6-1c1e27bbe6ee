package com.ylkj.system.service;

import java.util.List;

import com.ylkj.system.model.domain.OmgInviteVisit;
import com.ylkj.system.model.vo.omgInviteVisits.OmgInviteVisitsVo;
import com.ylkj.system.model.dto.omgInviteVisits.OmgInviteVisitsQuery;

import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
/**
 * 邀请码访问记录Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
public interface IOmgInviteVisitsService extends IService<OmgInviteVisit>
{
    //region mybatis代码
    /**
     * 查询邀请码访问记录
     * 
     * @param id 邀请码访问记录主键
     * @return 邀请码访问记录
     */
    public OmgInviteVisit selectOmgInviteVisitsById(Long id);

    /**
     * 查询邀请码访问记录列表
     * 
     * @param omgInviteVisit 邀请码访问记录
     * @return 邀请码访问记录集合
     */
    public List<OmgInviteVisit> selectOmgInviteVisitsList(OmgInviteVisit omgInviteVisit);

    /**
     * 新增邀请码访问记录
     * 
     * @param omgInviteVisit 邀请码访问记录
     * @return 结果
     */
    public int insertOmgInviteVisits(OmgInviteVisit omgInviteVisit);

    /**
     * 修改邀请码访问记录
     * 
     * @param omgInviteVisit 邀请码访问记录
     * @return 结果
     */
    public int updateOmgInviteVisits(OmgInviteVisit omgInviteVisit);

    /**
     * 批量删除邀请码访问记录
     * 
     * @param ids 需要删除的邀请码访问记录主键集合
     * @return 结果
     */
    public int deleteOmgInviteVisitsByIds(Long[] ids);

    /**
     * 删除邀请码访问记录信息
     * 
     * @param id 邀请码访问记录主键
     * @return 结果
     */
    public int deleteOmgInviteVisitsById(Long id);
    //endregion
    /**
     * 获取查询条件
     *
     * @param omgInviteVisitsQuery 查询条件对象
     * @return 查询条件
     */
    QueryWrapper<OmgInviteVisit> getQueryWrapper(OmgInviteVisitsQuery omgInviteVisitsQuery);

    /**
     * 转换vo
     *
     * @param omgInviteVisitList OmgInviteVisits集合
     * @return OmgInviteVisitsVO集合
     */
    List<OmgInviteVisitsVo> convertVoList(List<OmgInviteVisit> omgInviteVisitList);
}
