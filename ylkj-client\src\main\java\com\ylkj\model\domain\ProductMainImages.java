package com.ylkj.model.domain;

import java.io.Serializable;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 商品主图表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("product_main_images")
@ApiModel(value="ProductMainImages对象", description="商品主图表")
public class ProductMainImages implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "商品SKU")
    @TableField("sku")
    private String sku;

    @ApiModelProperty(value = "商品ID")
    @TableField("item_id")
    private String itemId;

    @ApiModelProperty(value = "商城类型")
    @TableField("mall_type")
    private String mallType;

    @ApiModelProperty(value = "原始图片URL")
    @TableField("original_image_url")
    private String originalImageUrl;

    @ApiModelProperty(value = "OSS图片URL")
    @TableField("oss_image_url")
    private String ossImageUrl;

    @ApiModelProperty(value = "图片文件名")
    @TableField("image_name")
    private String imageName;

    @ApiModelProperty(value = "图片大小(字节)")
    @TableField("image_size")
    private Long imageSize;

    @ApiModelProperty(value = "图片类型")
    @TableField("image_type")
    private String imageType;

    @ApiModelProperty(value = "显示顺序")
    @TableField("display_order")
    private Integer displayOrder;

    @ApiModelProperty(value = "是否为主图(0-否,1-是)")
    @TableField("is_main")
    private Integer isMain;

    @ApiModelProperty(value = "状态(0-禁用,1-启用)")
    @TableField("status")
    private String status;

    @ApiModelProperty(value = "图片来源")
    @TableField("source")
    private String source;

    @ApiModelProperty(value = "备注")
    @TableField("remark")
    private String remark;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("create_time")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("update_time")
    private Date updateTime;

    @ApiModelProperty(value = "创建者")
    @TableField("create_by")
    private String createBy;

    @ApiModelProperty(value = "更新者")
    @TableField("update_by")
    private String updateBy;
}
