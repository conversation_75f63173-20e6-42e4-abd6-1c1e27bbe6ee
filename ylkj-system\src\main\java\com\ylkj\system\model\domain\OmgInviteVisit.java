package com.ylkj.system.model.domain;

import java.io.Serializable;
import java.util.Map;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.ylkj.common.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonInclude;
/**
 * 邀请码访问记录对象 omg_invite_visits
 *
 * <AUTHOR>
 * @date 2025-07-23
 */
@TableName("omg_invite_visits")
@Data
public class OmgInviteVisit implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** id */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /** 邀请码 */
    @Excel(name = "邀请码")
    private String inviteCode;

    /** 访问者IP */
    @Excel(name = "访问者IP")
    private String visitorIp;

    /** 访问时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "访问时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date visitTime;

    /** 访问日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "访问日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date visitDate;

    /** 请求参数 */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @TableField(exist = false)
    private Map<String, Object> params;
}
