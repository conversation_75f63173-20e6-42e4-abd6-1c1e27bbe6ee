package com.ylkj.mapper;

import com.ylkj.model.domain.ProductMainImages;
import com.ylkj.model.vo.ProductMainImagesVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 商品主图表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
@Mapper
public interface ProductMainImagesMapper extends BaseMapper<ProductMainImages> {

    /**
     * 根据商品SKU获取主图列表
     *
     * @param sku 商品SKU
     * @return 主图列表
     */
    List<ProductMainImages> selectByProductSku(@Param("sku") String sku);

    /**
     * 根据商品ID获取主图列表
     *
     * @param itemId 商品ID
     * @return 主图列表
     */
    List<ProductMainImages> selectByItemId(@Param("itemId") String itemId);

    /**
     * 根据商品SKU获取主图（仅获取主图标记为1的）
     *
     * @param sku 商品SKU
     * @return 主图信息
     */
    ProductMainImages selectMainImageBySku(@Param("sku") String sku);

    /**
     * 根据商品ID获取主图（仅获取主图标记为1的）
     *
     * @param itemId 商品ID
     * @return 主图信息
     */
    ProductMainImages selectMainImageByItemId(@Param("itemId") String itemId);

    /**
     * 根据商品SKU获取主图VO列表（包含扩展信息）
     *
     * @param sku 商品SKU
     * @return 主图VO列表
     */
    List<ProductMainImagesVO> selectVOByProductSku(@Param("sku") String sku);

    /**
     * 根据商品ID获取主图VO列表（包含扩展信息）
     *
     * @param itemId 商品ID
     * @return 主图VO列表
     */
    List<ProductMainImagesVO> selectVOByItemId(@Param("itemId") String itemId);

    /**
     * 批量插入商品主图
     *
     * @param imageList 主图列表
     * @return 插入数量
     */
    int batchInsert(@Param("list") List<ProductMainImages> imageList);

    /**
     * 根据商品SKU删除主图
     *
     * @param sku 商品SKU
     * @return 删除数量
     */
    int deleteByProductSku(@Param("sku") String sku);

    /**
     * 根据商品ID删除主图
     *
     * @param itemId 商品ID
     * @return 删除数量
     */
    int deleteByItemId(@Param("itemId") String itemId);

    /**
     * 更新主图状态
     *
     * @param id 主图ID
     * @param status 状态
     * @return 更新数量
     */
    int updateStatus(@Param("id") Long id, @Param("status") String status);

    /**
     * 设置主图标记（将指定图片设为主图，其他设为非主图）
     *
     * @param sku 商品SKU
     * @param imageId 要设为主图的图片ID
     * @return 更新数量
     */
    int setMainImage(@Param("sku") String sku, @Param("imageId") Long imageId);

    /**
     * 根据条件查询主图列表
     *
     * @param productMainImages 查询条件
     * @return 主图列表
     */
    List<ProductMainImages> selectByCondition(ProductMainImages productMainImages);

    /**
     * 根据条件查询主图VO列表
     *
     * @param productMainImages 查询条件
     * @return 主图VO列表
     */
    List<ProductMainImagesVO> selectVOByCondition(ProductMainImages productMainImages);
}
