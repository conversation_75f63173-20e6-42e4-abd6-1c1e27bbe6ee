package com.ylkj.model.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 商品主图DTO对象
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="ProductMainImagesDTO对象", description="商品主图DTO")
public class ProductMainImagesDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "商品SKU", required = true)
    @NotBlank(message = "商品SKU不能为空")
    private String sku;

    @ApiModelProperty(value = "商品ID")
    private String itemId;

    @ApiModelProperty(value = "商城类型")
    private String mallType;

    @ApiModelProperty(value = "原始图片URL", required = true)
    @NotBlank(message = "原始图片URL不能为空")
    private String originalImageUrl;

    @ApiModelProperty(value = "OSS图片URL")
    private String ossImageUrl;

    @ApiModelProperty(value = "图片文件名")
    private String imageName;

    @ApiModelProperty(value = "图片大小(字节)")
    private Long imageSize;

    @ApiModelProperty(value = "图片类型")
    private String imageType;

    @ApiModelProperty(value = "显示顺序")
    private Integer displayOrder;

    @ApiModelProperty(value = "是否为主图(0-否,1-是)")
    private Integer isMain;

    @ApiModelProperty(value = "状态(0-禁用,1-启用)")
    private String status;

    @ApiModelProperty(value = "图片来源")
    private String source;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "创建者")
    private String createBy;

    @ApiModelProperty(value = "更新者")
    private String updateBy;

    // 批量操作相关字段
    @ApiModelProperty(value = "批量图片URL列表")
    private List<String> imageUrls;

    @ApiModelProperty(value = "批量操作的商品SKU列表")
    private List<String> skuList;

    @ApiModelProperty(value = "批量操作的商品ID列表")
    private List<String> itemIdList;

    // 查询条件相关字段
    @ApiModelProperty(value = "开始时间")
    private String startTime;

    @ApiModelProperty(value = "结束时间")
    private String endTime;

    @ApiModelProperty(value = "关键词搜索")
    private String keyword;

    @ApiModelProperty(value = "页码")
    private Integer pageNum;

    @ApiModelProperty(value = "页大小")
    private Integer pageSize;

    // 图片处理相关字段
    @ApiModelProperty(value = "是否需要压缩")
    private Boolean needCompress;

    @ApiModelProperty(value = "压缩质量(1-100)")
    private Integer compressQuality;

    @ApiModelProperty(value = "目标宽度")
    private Integer targetWidth;

    @ApiModelProperty(value = "目标高度")
    private Integer targetHeight;

    @ApiModelProperty(value = "是否保持宽高比")
    private Boolean keepAspectRatio;

    // 上传相关字段
    @ApiModelProperty(value = "上传方式(LOCAL-本地, OSS-阿里云)")
    private String uploadType;

    @ApiModelProperty(value = "上传目录")
    private String uploadDir;

    @ApiModelProperty(value = "是否覆盖同名文件")
    private Boolean overwrite;

    // 同步相关字段
    @ApiModelProperty(value = "是否同步到OSS")
    private Boolean syncToOss;

    @ApiModelProperty(value = "是否生成缩略图")
    private Boolean generateThumbnail;

    @ApiModelProperty(value = "缩略图尺寸")
    private String thumbnailSize;
}
