package com.ylkj.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ylkj.model.domain.OmgInviteVisits;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 邀请码访问记录客户端Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-23
 */
@Mapper
public interface OmgInviteVisitsClientMapper extends BaseMapper<OmgInviteVisits> {
    
    /**
     * 检查是否存在重复访问记录（防刷）
     * 
     * @param inviteCode 邀请码
     * @param visitorIp 访问者IP
     * @param minutes 时间间隔（分钟）
     * @return 记录数量
     */
    @Select("SELECT COUNT(*) FROM omg_invite_visits WHERE invite_code = #{inviteCode} AND visitor_ip = #{visitorIp} AND visit_time > DATE_SUB(NOW(), INTERVAL #{minutes} MINUTE)")
    int checkDuplicateVisit(@Param("inviteCode") String inviteCode, 
                           @Param("visitorIp") String visitorIp, 
                           @Param("minutes") int minutes);
}
