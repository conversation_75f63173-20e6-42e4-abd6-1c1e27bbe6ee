package com.ylkj.system.model.dto.omgInviteCodes;

import java.io.Serializable;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.beans.BeanUtils;
import com.ylkj.system.model.domain.OmgInviteCodes;
/**
 * 邀请码Vo对象 omg_invite_codes
 *
 * <AUTHOR>
 * @date 2025-07-23
 */
@Data
public class OmgInviteCodesInsert implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 邀请码 */
    private String inviteCode;

    /** 邀请码名称 */
    private String codeName;

    /** 总访问次数 */
    private Long totalVisits;

    /** 总注册人数 */
    private Long totalRegistrations;

    /** 状态：active/inactive */
    private String status;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date createdAt;

    /**
     * 对象转封装类
     *
     * @param omgInviteCodesInsert 插入对象
     * @return OmgInviteCodesInsert
     */
    public static OmgInviteCodes insertToObj(OmgInviteCodesInsert omgInviteCodesInsert) {
        if (omgInviteCodesInsert == null) {
            return null;
        }
        OmgInviteCodes omgInviteCodes = new OmgInviteCodes();
        BeanUtils.copyProperties(omgInviteCodesInsert, omgInviteCodes);
        return omgInviteCodes;
    }
}
