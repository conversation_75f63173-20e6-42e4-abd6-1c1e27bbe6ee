package com.ylkj.system.service.impl;

import java.util.*;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.stream.Collectors;
import com.ylkj.common.utils.StringUtils;
import java.util.Date;
import javax.annotation.Resource;

import com.ylkj.system.model.domain.OmgInviteVisit;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ylkj.system.mapper.OmgInviteVisitMapper;
import com.ylkj.system.service.IOmgInviteVisitsService;
import com.ylkj.system.model.dto.omgInviteVisits.OmgInviteVisitsQuery;
import com.ylkj.system.model.vo.omgInviteVisits.OmgInviteVisitsVo;

/**
 * 邀请码访问记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
@Service
public class OmgInviteVisitsServiceImpl extends ServiceImpl<OmgInviteVisitMapper, OmgInviteVisit> implements IOmgInviteVisitsService
{
    @Resource
    private OmgInviteVisitMapper omgInviteVisitsMapper;

    //region mybatis代码
    /**
     * 查询邀请码访问记录
     * 
     * @param id 邀请码访问记录主键
     * @return 邀请码访问记录
     */
    @Override
    public OmgInviteVisit selectOmgInviteVisitsById(Long id)
    {
        return omgInviteVisitsMapper.selectOmgInviteVisitsById(id);
    }

    /**
     * 查询邀请码访问记录列表
     * 
     * @param omgInviteVisit 邀请码访问记录
     * @return 邀请码访问记录
     */
    @Override
    public List<OmgInviteVisit> selectOmgInviteVisitsList(OmgInviteVisit omgInviteVisit)
    {
        return omgInviteVisitsMapper.selectOmgInviteVisitsList(omgInviteVisit);
    }

    /**
     * 新增邀请码访问记录
     * 
     * @param omgInviteVisit 邀请码访问记录
     * @return 结果
     */
    @Override
    public int insertOmgInviteVisits(OmgInviteVisit omgInviteVisit)
    {
        return omgInviteVisitsMapper.insertOmgInviteVisits(omgInviteVisit);
    }

    /**
     * 修改邀请码访问记录
     * 
     * @param omgInviteVisit 邀请码访问记录
     * @return 结果
     */
    @Override
    public int updateOmgInviteVisits(OmgInviteVisit omgInviteVisit)
    {
        return omgInviteVisitsMapper.updateOmgInviteVisits(omgInviteVisit);
    }

    /**
     * 批量删除邀请码访问记录
     * 
     * @param ids 需要删除的邀请码访问记录主键
     * @return 结果
     */
    @Override
    public int deleteOmgInviteVisitsByIds(Long[] ids)
    {
        return omgInviteVisitsMapper.deleteOmgInviteVisitsByIds(ids);
    }

    /**
     * 删除邀请码访问记录信息
     * 
     * @param id 邀请码访问记录主键
     * @return 结果
     */
    @Override
    public int deleteOmgInviteVisitsById(Long id)
    {
        return omgInviteVisitsMapper.deleteOmgInviteVisitsById(id);
    }
    //endregion
    @Override
    public QueryWrapper<OmgInviteVisit> getQueryWrapper(OmgInviteVisitsQuery omgInviteVisitsQuery){
        QueryWrapper<OmgInviteVisit> queryWrapper = new QueryWrapper<>();
        //如果不使用params可以删除
        Map<String, Object> params = omgInviteVisitsQuery.getParams();
        if (StringUtils.isNull(params)) {
            params = new HashMap<>();
        }
        String inviteCode = omgInviteVisitsQuery.getInviteCode();
        queryWrapper.eq(StringUtils.isNotEmpty(inviteCode) ,"invite_code",inviteCode);

        String visitorIp = omgInviteVisitsQuery.getVisitorIp();
        queryWrapper.eq(StringUtils.isNotEmpty(visitorIp) ,"visitor_ip",visitorIp);

        Date visitTime = omgInviteVisitsQuery.getVisitTime();
        queryWrapper.eq( StringUtils.isNotNull(visitTime),"visit_time",visitTime);

        Date visitDate = omgInviteVisitsQuery.getVisitDate();
        queryWrapper.eq( StringUtils.isNotNull(visitDate),"visit_date",visitDate);

        return queryWrapper;
    }

    @Override
    public List<OmgInviteVisitsVo> convertVoList(List<OmgInviteVisit> omgInviteVisitList) {
        if (StringUtils.isEmpty(omgInviteVisitList)) {
            return Collections.emptyList();
        }
        return omgInviteVisitList.stream().map(OmgInviteVisitsVo::objToVo).collect(Collectors.toList());
    }

}
