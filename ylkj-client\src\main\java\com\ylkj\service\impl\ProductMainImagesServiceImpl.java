package com.ylkj.service.impl;

import com.ylkj.model.domain.ProductMainImages;
import com.ylkj.model.vo.ProductMainImagesVO;
import com.ylkj.mapper.ProductMainImagesMapper;
import com.ylkj.service.IProductMainImagesService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 商品主图表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
@Service
public class ProductMainImagesServiceImpl extends ServiceImpl<ProductMainImagesMapper, ProductMainImages> implements IProductMainImagesService {

    private static final Logger log = LoggerFactory.getLogger(ProductMainImagesServiceImpl.class);

    @Autowired
    private ProductMainImagesMapper productMainImagesMapper;

    @Override
    public List<ProductMainImages> getByProductSku(String sku) {
        try {
            return productMainImagesMapper.selectByProductSku(sku);
        } catch (Exception e) {
            log.error("根据商品SKU获取主图列表失败, sku: {}", sku, e);
            throw new RuntimeException("获取商品主图失败", e);
        }
    }

    @Override
    public List<ProductMainImages> getByItemId(String itemId) {
        try {
            return productMainImagesMapper.selectByItemId(itemId);
        } catch (Exception e) {
            log.error("根据商品ID获取主图列表失败, itemId: {}", itemId, e);
            throw new RuntimeException("获取商品主图失败", e);
        }
    }

    @Override
    public ProductMainImages getMainImageBySku(String sku) {
        try {
            return productMainImagesMapper.selectMainImageBySku(sku);
        } catch (Exception e) {
            log.error("根据商品SKU获取主图失败, sku: {}", sku, e);
            throw new RuntimeException("获取商品主图失败", e);
        }
    }

    @Override
    public ProductMainImages getMainImageByItemId(String itemId) {
        try {
            return productMainImagesMapper.selectMainImageByItemId(itemId);
        } catch (Exception e) {
            log.error("根据商品ID获取主图失败, itemId: {}", itemId, e);
            throw new RuntimeException("获取商品主图失败", e);
        }
    }

    @Override
    public List<ProductMainImagesVO> getVOByProductSku(String sku) {
        try {
            return productMainImagesMapper.selectVOByProductSku(sku);
        } catch (Exception e) {
            log.error("根据商品SKU获取主图VO列表失败, sku: {}", sku, e);
            throw new RuntimeException("获取商品主图失败", e);
        }
    }

    @Override
    public List<ProductMainImagesVO> getVOByItemId(String itemId) {
        try {
            return productMainImagesMapper.selectVOByItemId(itemId);
        } catch (Exception e) {
            log.error("根据商品ID获取主图VO列表失败, itemId: {}", itemId, e);
            throw new RuntimeException("获取商品主图失败", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveProductMainImage(ProductMainImages productMainImages) {
        try {
            // 设置创建时间
            if (productMainImages.getCreateTime() == null) {
                productMainImages.setCreateTime(new Date());
            }
            productMainImages.setUpdateTime(new Date());
            
            return save(productMainImages);
        } catch (Exception e) {
            log.error("保存商品主图失败, productMainImages: {}", productMainImages, e);
            throw new RuntimeException("保存商品主图失败", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchSaveProductMainImages(List<ProductMainImages> imageList) {
        try {
            if (imageList == null || imageList.isEmpty()) {
                return true;
            }
            
            // 设置创建时间
            Date now = new Date();
            for (ProductMainImages image : imageList) {
                if (image.getCreateTime() == null) {
                    image.setCreateTime(now);
                }
                image.setUpdateTime(now);
            }
            
            return productMainImagesMapper.batchInsert(imageList) > 0;
        } catch (Exception e) {
            log.error("批量保存商品主图失败, imageList size: {}", imageList != null ? imageList.size() : 0, e);
            throw new RuntimeException("批量保存商品主图失败", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateProductMainImage(ProductMainImages productMainImages) {
        try {
            productMainImages.setUpdateTime(new Date());
            return updateById(productMainImages);
        } catch (Exception e) {
            log.error("更新商品主图失败, productMainImages: {}", productMainImages, e);
            throw new RuntimeException("更新商品主图失败", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteById(Long id) {
        try {
            return removeById(id);
        } catch (Exception e) {
            log.error("删除商品主图失败, id: {}", id, e);
            throw new RuntimeException("删除商品主图失败", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByProductSku(String sku) {
        try {
            return productMainImagesMapper.deleteByProductSku(sku) >= 0;
        } catch (Exception e) {
            log.error("根据商品SKU删除主图失败, sku: {}", sku, e);
            throw new RuntimeException("删除商品主图失败", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByItemId(String itemId) {
        try {
            return productMainImagesMapper.deleteByItemId(itemId) >= 0;
        } catch (Exception e) {
            log.error("根据商品ID删除主图失败, itemId: {}", itemId, e);
            throw new RuntimeException("删除商品主图失败", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateStatus(Long id, String status) {
        try {
            return productMainImagesMapper.updateStatus(id, status) > 0;
        } catch (Exception e) {
            log.error("更新主图状态失败, id: {}, status: {}", id, status, e);
            throw new RuntimeException("更新主图状态失败", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean setMainImage(String sku, Long imageId) {
        try {
            return productMainImagesMapper.setMainImage(sku, imageId) > 0;
        } catch (Exception e) {
            log.error("设置主图标记失败, sku: {}, imageId: {}", sku, imageId, e);
            throw new RuntimeException("设置主图标记失败", e);
        }
    }

    @Override
    public PageInfo<ProductMainImages> getPageList(ProductMainImages productMainImages, int pageNum, int pageSize) {
        try {
            PageHelper.startPage(pageNum, pageSize);
            List<ProductMainImages> list = productMainImagesMapper.selectByCondition(productMainImages);
            return new PageInfo<>(list);
        } catch (Exception e) {
            log.error("分页查询主图列表失败, pageNum: {}, pageSize: {}", pageNum, pageSize, e);
            throw new RuntimeException("查询主图列表失败", e);
        }
    }

    @Override
    public PageInfo<ProductMainImagesVO> getVOPageList(ProductMainImages productMainImages, int pageNum, int pageSize) {
        try {
            PageHelper.startPage(pageNum, pageSize);
            List<ProductMainImagesVO> list = productMainImagesMapper.selectVOByCondition(productMainImages);
            return new PageInfo<>(list);
        } catch (Exception e) {
            log.error("分页查询主图VO列表失败, pageNum: {}, pageSize: {}", pageNum, pageSize, e);
            throw new RuntimeException("查询主图列表失败", e);
        }
    }

    @Override
    public List<ProductMainImages> getByCondition(ProductMainImages productMainImages) {
        try {
            return productMainImagesMapper.selectByCondition(productMainImages);
        } catch (Exception e) {
            log.error("根据条件查询主图列表失败, condition: {}", productMainImages, e);
            throw new RuntimeException("查询主图列表失败", e);
        }
    }

    @Override
    public List<ProductMainImagesVO> getVOByCondition(ProductMainImages productMainImages) {
        try {
            return productMainImagesMapper.selectVOByCondition(productMainImages);
        } catch (Exception e) {
            log.error("根据条件查询主图VO列表失败, condition: {}", productMainImages, e);
            throw new RuntimeException("查询主图列表失败", e);
        }
    }
}
