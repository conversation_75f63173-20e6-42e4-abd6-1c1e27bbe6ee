package com.ylkj.system.service;

import com.ylkj.system.model.dto.inviteStats.InviteTimeStatsDto;
import java.util.List;
import java.util.Map;

/**
 * 邀请码时间维度统计Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-23
 */
public interface IInviteTimeStatsService {
    
    /**
     * 获取每日统计数据
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param inviteCode 邀请码（可选）
     * @return 每日统计数据
     */
    List<InviteTimeStatsDto> getDailyStats(String startDate, String endDate, String inviteCode);
    
    /**
     * 获取每周统计数据
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param inviteCode 邀请码（可选）
     * @return 每周统计数据
     */
    List<InviteTimeStatsDto> getWeeklyStats(String startDate, String endDate, String inviteCode);
    
    /**
     * 获取每月统计数据
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param inviteCode 邀请码（可选）
     * @return 每月统计数据
     */
    List<InviteTimeStatsDto> getMonthlyStats(String startDate, String endDate, String inviteCode);
}
