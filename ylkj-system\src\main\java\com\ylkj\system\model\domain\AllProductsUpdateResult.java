package com.ylkj.system.model.domain;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Author: 吴居乐
 * @Description: TODO
 * @DateTime: 2025/7/28/周一 18:13
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AllProductsUpdateResult {

    /**
     * 总数
     */
    private int total = 0;
    /**
     * 成功数
     */
    private int success = 0;
    /**
     * 失败数
     */
    private int failed = 0;
    /**
     * 跳过数
     */
    private int skipped = 0;
    /**
     * 不支持数
     */
    private int unsupported = 0;
    /**
     * 成功的更新信息
     */
    private List<ProductUpdateInfo> successProducts = new ArrayList<>();
    /**
     * 失败的更新信息
     */
    private Map<String, String> failedProducts = new HashMap<>();
    private Map<String, String> skippedProducts = new HashMap<>();
    private Map<String, String> unsupportedProducts = new HashMap<>();

    public void incrementSuccess() { this.success++; }
    public void incrementFailed() { this.failed++; }
    public void incrementSkipped() { this.skipped++; }
    public void incrementUnsupported() { this.unsupported++; }

    public void addSuccessProduct(String sku, String mainImageUrl, int imageCount) {
        this.successProducts.add(new ProductUpdateInfo(sku, mainImageUrl, imageCount));
    }
    public void addFailedProduct(String sku, String reason) {
        this.failedProducts.put(sku, reason);
    }
    public void addSkippedProduct(String sku, String reason) {
        this.skippedProducts.put(sku, reason);
    }
    public void addUnsupportedProduct(String sku, String reason) {
        this.unsupportedProducts.put(sku, reason);
    }

}
