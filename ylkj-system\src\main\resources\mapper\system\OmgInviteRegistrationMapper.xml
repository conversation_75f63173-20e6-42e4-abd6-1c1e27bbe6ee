<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ylkj.system.mapper.OmgInviteRegistrationMapper">
    
    <resultMap type="OmgInviteRegistration" id="OmgInviteRegistrationResult">
        <result property="id"    column="id"    />
        <result property="inviteCode"    column="invite_code"    />
        <result property="userId"    column="user_id"    />
        <result property="registerTime"    column="register_time"    />
        <result property="registerDate"    column="register_date"    />
    </resultMap>

    <sql id="selectOmgInviteRegistrationVo">
        select id, invite_code, user_id, register_time, register_date from omg_invite_registrations
    </sql>

    <select id="selectOmgInviteRegistrationList" parameterType="OmgInviteRegistration" resultMap="OmgInviteRegistrationResult">
        <include refid="selectOmgInviteRegistrationVo"/>
        <where>  
            <if test="inviteCode != null  and inviteCode != ''"> and invite_code = #{inviteCode}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="registerTime != null "> and register_time = #{registerTime}</if>
            <if test="registerDate != null "> and register_date = #{registerDate}</if>
        </where>
    </select>
    
    <select id="selectOmgInviteRegistrationById" parameterType="Long" resultMap="OmgInviteRegistrationResult">
        <include refid="selectOmgInviteRegistrationVo"/>
        where id = #{id}
    </select>

    <insert id="insertOmgInviteRegistration" parameterType="OmgInviteRegistration" useGeneratedKeys="true" keyProperty="id">
        insert into omg_invite_registrations
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="inviteCode != null and inviteCode != ''">invite_code,</if>
            <if test="userId != null">user_id,</if>
            <if test="registerTime != null">register_time,</if>
            <if test="registerDate != null">register_date,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="inviteCode != null and inviteCode != ''">#{inviteCode},</if>
            <if test="userId != null">#{userId},</if>
            <if test="registerTime != null">#{registerTime},</if>
            <if test="registerDate != null">#{registerDate},</if>
         </trim>
    </insert>

    <update id="updateOmgInviteRegistration" parameterType="OmgInviteRegistration">
        update omg_invite_registrations
        <trim prefix="SET" suffixOverrides=",">
            <if test="inviteCode != null and inviteCode != ''">invite_code = #{inviteCode},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="registerTime != null">register_time = #{registerTime},</if>
            <if test="registerDate != null">register_date = #{registerDate},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteOmgInviteRegistrationById" parameterType="Long">
        delete from omg_invite_registrations where id = #{id}
    </delete>

    <delete id="deleteOmgInviteRegistrationByIds" parameterType="String">
        delete from omg_invite_registrations where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>