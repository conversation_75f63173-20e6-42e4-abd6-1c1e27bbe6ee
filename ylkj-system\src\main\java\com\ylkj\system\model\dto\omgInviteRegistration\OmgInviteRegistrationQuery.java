package com.ylkj.system.model.dto.omgInviteRegistration;

import java.util.Map;
import java.io.Serializable;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import org.springframework.beans.BeanUtils;
import com.baomidou.mybatisplus.annotation.TableField;
import com.ylkj.system.model.domain.OmgInviteRegistration;
/**
 * 邀请码注册记录Query对象 omg_invite_registrations
 *
 * <AUTHOR>
 * @date 2025-07-23
 */
@Data
public class OmgInviteRegistrationQuery implements Serializable
{
    private static final long serialVersionUID = 1L;
    private Long id;
    /** 邀请码 */
    private String inviteCode;

    /** 注册的用户ID */
    private Long userId;

    /** 注册时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date registerTime;

    /** 注册日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date registerDate;

    /** 请求参数 */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @TableField(exist = false)
    private Map<String, Object> params;

    /**
     * 对象转封装类
     *
     * @param omgInviteRegistrationQuery 查询对象
     * @return OmgInviteRegistration
     */
    public static OmgInviteRegistration queryToObj(OmgInviteRegistrationQuery omgInviteRegistrationQuery) {
        if (omgInviteRegistrationQuery == null) {
            return null;
        }
        OmgInviteRegistration omgInviteRegistration = new OmgInviteRegistration();
        BeanUtils.copyProperties(omgInviteRegistrationQuery, omgInviteRegistration);
        return omgInviteRegistration;
    }
}
