<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ylkj.system.mapper.OmgInviteCodesMapper">
    
    <resultMap type="OmgInviteCodes" id="OmgInviteCodesResult">
        <result property="id"    column="id"    />
        <result property="inviteCode"    column="invite_code"    />
        <result property="codeName"    column="code_name"    />
        <result property="totalVisits"    column="total_visits"    />
        <result property="totalRegistrations"    column="total_registrations"    />
        <result property="status"    column="status"    />
        <result property="createdAt"    column="created_at"    />
    </resultMap>

    <sql id="selectOmgInviteCodesVo">
        select id, invite_code, code_name, total_visits, total_registrations, status, created_at from omg_invite_codes
    </sql>

    <select id="selectOmgInviteCodesList" parameterType="OmgInviteCodes" resultMap="OmgInviteCodesResult">
        <include refid="selectOmgInviteCodesVo"/>
        <where>  
            <if test="inviteCode != null  and inviteCode != ''"> and invite_code = #{inviteCode}</if>
            <if test="codeName != null  and codeName != ''"> and code_name like concat('%', #{codeName}, '%')</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="createdAt != null "> and created_at = #{createdAt}</if>
        </where>
    </select>
    
    <select id="selectOmgInviteCodesById" parameterType="Long" resultMap="OmgInviteCodesResult">
        <include refid="selectOmgInviteCodesVo"/>
        where id = #{id}
    </select>

    <insert id="insertOmgInviteCodes" parameterType="OmgInviteCodes" useGeneratedKeys="true" keyProperty="id">
        insert into omg_invite_codes
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="inviteCode != null and inviteCode != ''">invite_code,</if>
            <if test="codeName != null">code_name,</if>
            <if test="totalVisits != null">total_visits,</if>
            <if test="totalRegistrations != null">total_registrations,</if>
            <if test="status != null">status,</if>
            <if test="createdAt != null">created_at,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="inviteCode != null and inviteCode != ''">#{inviteCode},</if>
            <if test="codeName != null">#{codeName},</if>
            <if test="totalVisits != null">#{totalVisits},</if>
            <if test="totalRegistrations != null">#{totalRegistrations},</if>
            <if test="status != null">#{status},</if>
            <if test="createdAt != null">#{createdAt},</if>
         </trim>
    </insert>

    <update id="updateOmgInviteCodes" parameterType="OmgInviteCodes">
        update omg_invite_codes
        <trim prefix="SET" suffixOverrides=",">
            <if test="inviteCode != null and inviteCode != ''">invite_code = #{inviteCode},</if>
            <if test="codeName != null">code_name = #{codeName},</if>
            <if test="totalVisits != null">total_visits = #{totalVisits},</if>
            <if test="totalRegistrations != null">total_registrations = #{totalRegistrations},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createdAt != null">created_at = #{createdAt},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteOmgInviteCodesById" parameterType="Long">
        delete from omg_invite_codes where id = #{id}
    </delete>

    <delete id="deleteOmgInviteCodesByIds" parameterType="String">
        delete from omg_invite_codes where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 按天统计邀请码数据 -->
    <select id="selectDailyStats" resultType="com.ylkj.system.model.dto.inviteStats.InviteTimeStatsDto">
        WITH date_range AS (
            SELECT DISTINCT DATE(visit_time) as timeLabel
            FROM omg_invite_visits
            WHERE 1=1
            <if test="startDate != null and startDate != ''">
                AND DATE(visit_time) &gt;= #{startDate}
            </if>
            <if test="endDate != null and endDate != ''">
                AND DATE(visit_time) &lt;= #{endDate}
            </if>
            <if test="inviteCode != null and inviteCode != ''">
                AND invite_code = #{inviteCode}
            </if>
            UNION
            SELECT DISTINCT DATE(register_time) as timeLabel
            FROM omg_invite_registrations
            WHERE 1=1
            <if test="startDate != null and startDate != ''">
                AND DATE(register_time) &gt;= #{startDate}
            </if>
            <if test="endDate != null and endDate != ''">
                AND DATE(register_time) &lt;= #{endDate}
            </if>
            <if test="inviteCode != null and inviteCode != ''">
                AND invite_code = #{inviteCode}
            </if>
        ),
        visit_stats AS (
            SELECT
                DATE(visit_time) as visit_date,
                COUNT(*) as visit_count
            FROM omg_invite_visits
            WHERE 1=1
            <if test="startDate != null and startDate != ''">
                AND DATE(visit_time) &gt;= #{startDate}
            </if>
            <if test="endDate != null and endDate != ''">
                AND DATE(visit_time) &lt;= #{endDate}
            </if>
            <if test="inviteCode != null and inviteCode != ''">
                AND invite_code = #{inviteCode}
            </if>
            GROUP BY DATE(visit_time)
        ),
        registration_stats AS (
            SELECT
                DATE(register_time) as register_date,
                COUNT(*) as registration_count
            FROM omg_invite_registrations
            WHERE 1=1
            <if test="startDate != null and startDate != ''">
                AND DATE(register_time) &gt;= #{startDate}
            </if>
            <if test="endDate != null and endDate != ''">
                AND DATE(register_time) &lt;= #{endDate}
            </if>
            <if test="inviteCode != null and inviteCode != ''">
                AND invite_code = #{inviteCode}
            </if>
            GROUP BY DATE(register_time)
        )
        SELECT
            dr.timeLabel,
            COALESCE(vs.visit_count, 0) as visitCount,
            COALESCE(rs.registration_count, 0) as registrationCount,
            CASE
                WHEN COALESCE(vs.visit_count, 0) = 0 THEN 0.0
                ELSE ROUND(COALESCE(rs.registration_count, 0) * 100.0 / vs.visit_count, 2)
            END as conversionRate
        FROM date_range dr
        LEFT JOIN visit_stats vs ON dr.timeLabel = vs.visit_date
        LEFT JOIN registration_stats rs ON dr.timeLabel = rs.register_date
        ORDER BY dr.timeLabel
    </select>

    <!-- 按周统计邀请码数据 -->
    <select id="selectWeeklyStats" resultType="com.ylkj.system.model.dto.inviteStats.InviteTimeStatsDto">
        WITH week_range AS (
            SELECT DISTINCT CONCAT(YEAR(visit_time), '-W', LPAD(WEEK(visit_time, 1), 2, '0')) as timeLabel
            FROM omg_invite_visits
            WHERE 1=1
            <if test="startDate != null and startDate != ''">
                AND DATE(visit_time) &gt;= #{startDate}
            </if>
            <if test="endDate != null and endDate != ''">
                AND DATE(visit_time) &lt;= #{endDate}
            </if>
            <if test="inviteCode != null and inviteCode != ''">
                AND invite_code = #{inviteCode}
            </if>
            UNION
            SELECT DISTINCT CONCAT(YEAR(register_time), '-W', LPAD(WEEK(register_time, 1), 2, '0')) as timeLabel
            FROM omg_invite_registrations
            WHERE 1=1
            <if test="startDate != null and startDate != ''">
                AND DATE(register_time) &gt;= #{startDate}
            </if>
            <if test="endDate != null and endDate != ''">
                AND DATE(register_time) &lt;= #{endDate}
            </if>
            <if test="inviteCode != null and inviteCode != ''">
                AND invite_code = #{inviteCode}
            </if>
        ),
        visit_stats AS (
            SELECT
                CONCAT(YEAR(visit_time), '-W', LPAD(WEEK(visit_time, 1), 2, '0')) as week_label,
                COUNT(*) as visit_count
            FROM omg_invite_visits
            WHERE 1=1
            <if test="startDate != null and startDate != ''">
                AND DATE(visit_time) &gt;= #{startDate}
            </if>
            <if test="endDate != null and endDate != ''">
                AND DATE(visit_time) &lt;= #{endDate}
            </if>
            <if test="inviteCode != null and inviteCode != ''">
                AND invite_code = #{inviteCode}
            </if>
            GROUP BY CONCAT(YEAR(visit_time), '-W', LPAD(WEEK(visit_time, 1), 2, '0'))
        ),
        registration_stats AS (
            SELECT
                CONCAT(YEAR(register_time), '-W', LPAD(WEEK(register_time, 1), 2, '0')) as week_label,
                COUNT(*) as registration_count
            FROM omg_invite_registrations
            WHERE 1=1
            <if test="startDate != null and startDate != ''">
                AND DATE(register_time) &gt;= #{startDate}
            </if>
            <if test="endDate != null and endDate != ''">
                AND DATE(register_time) &lt;= #{endDate}
            </if>
            <if test="inviteCode != null and inviteCode != ''">
                AND invite_code = #{inviteCode}
            </if>
            GROUP BY CONCAT(YEAR(register_time), '-W', LPAD(WEEK(register_time, 1), 2, '0'))
        )
        SELECT
            wr.timeLabel,
            COALESCE(vs.visit_count, 0) as visitCount,
            COALESCE(rs.registration_count, 0) as registrationCount,
            CASE
                WHEN COALESCE(vs.visit_count, 0) = 0 THEN 0.0
                ELSE ROUND(COALESCE(rs.registration_count, 0) * 100.0 / vs.visit_count, 2)
            END as conversionRate
        FROM week_range wr
        LEFT JOIN visit_stats vs ON wr.timeLabel = vs.week_label
        LEFT JOIN registration_stats rs ON wr.timeLabel = rs.week_label
        ORDER BY wr.timeLabel
    </select>

    <!-- 按月统计邀请码数据 -->
    <select id="selectMonthlyStats" resultType="com.ylkj.system.model.dto.inviteStats.InviteTimeStatsDto">
        WITH month_range AS (
            SELECT DISTINCT DATE_FORMAT(visit_time, '%Y-%m') as timeLabel
            FROM omg_invite_visits
            WHERE 1=1
            <if test="startDate != null and startDate != ''">
                AND DATE(visit_time) &gt;= #{startDate}
            </if>
            <if test="endDate != null and endDate != ''">
                AND DATE(visit_time) &lt;= #{endDate}
            </if>
            <if test="inviteCode != null and inviteCode != ''">
                AND invite_code = #{inviteCode}
            </if>
            UNION
            SELECT DISTINCT DATE_FORMAT(register_time, '%Y-%m') as timeLabel
            FROM omg_invite_registrations
            WHERE 1=1
            <if test="startDate != null and startDate != ''">
                AND DATE(register_time) &gt;= #{startDate}
            </if>
            <if test="endDate != null and endDate != ''">
                AND DATE(register_time) &lt;= #{endDate}
            </if>
            <if test="inviteCode != null and inviteCode != ''">
                AND invite_code = #{inviteCode}
            </if>
        ),
        visit_stats AS (
            SELECT
                DATE_FORMAT(visit_time, '%Y-%m') as month_label,
                COUNT(*) as visit_count
            FROM omg_invite_visits
            WHERE 1=1
            <if test="startDate != null and startDate != ''">
                AND DATE(visit_time) &gt;= #{startDate}
            </if>
            <if test="endDate != null and endDate != ''">
                AND DATE(visit_time) &lt;= #{endDate}
            </if>
            <if test="inviteCode != null and inviteCode != ''">
                AND invite_code = #{inviteCode}
            </if>
            GROUP BY DATE_FORMAT(visit_time, '%Y-%m')
        ),
        registration_stats AS (
            SELECT
                DATE_FORMAT(register_time, '%Y-%m') as month_label,
                COUNT(*) as registration_count
            FROM omg_invite_registrations
            WHERE 1=1
            <if test="startDate != null and startDate != ''">
                AND DATE(register_time) &gt;= #{startDate}
            </if>
            <if test="endDate != null and endDate != ''">
                AND DATE(register_time) &lt;= #{endDate}
            </if>
            <if test="inviteCode != null and inviteCode != ''">
                AND invite_code = #{inviteCode}
            </if>
            GROUP BY DATE_FORMAT(register_time, '%Y-%m')
        )
        SELECT
            mr.timeLabel,
            COALESCE(vs.visit_count, 0) as visitCount,
            COALESCE(rs.registration_count, 0) as registrationCount,
            CASE
                WHEN COALESCE(vs.visit_count, 0) = 0 THEN 0.0
                ELSE ROUND(COALESCE(rs.registration_count, 0) * 100.0 / vs.visit_count, 2)
            END as conversionRate
        FROM month_range mr
        LEFT JOIN visit_stats vs ON mr.timeLabel = vs.month_label
        LEFT JOIN registration_stats rs ON mr.timeLabel = rs.month_label
        ORDER BY mr.timeLabel
    </select>
</mapper>