package com.ylkj.system.service.impl;

import java.util.*;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.stream.Collectors;
import com.ylkj.common.utils.StringUtils;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ylkj.system.mapper.OmgInviteRegistrationMapper;
import com.ylkj.system.model.domain.OmgInviteRegistration;
import com.ylkj.system.service.IOmgInviteRegistrationService;
import com.ylkj.system.model.dto.omgInviteRegistration.OmgInviteRegistrationQuery;
import com.ylkj.system.model.vo.omgInviteRegistration.OmgInviteRegistrationVo;

/**
 * 邀请码注册记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
@Service
public class OmgInviteRegistrationServiceImpl extends ServiceImpl<OmgInviteRegistrationMapper, OmgInviteRegistration> implements IOmgInviteRegistrationService
{
    @Resource
    private OmgInviteRegistrationMapper omgInviteRegistrationMapper;

    //region mybatis代码
    /**
     * 查询邀请码注册记录
     * 
     * @param id 邀请码注册记录主键
     * @return 邀请码注册记录
     */
    @Override
    public OmgInviteRegistration selectOmgInviteRegistrationById(Long id)
    {
        return omgInviteRegistrationMapper.selectOmgInviteRegistrationById(id);
    }

    /**
     * 查询邀请码注册记录列表
     * 
     * @param omgInviteRegistration 邀请码注册记录
     * @return 邀请码注册记录
     */
    @Override
    public List<OmgInviteRegistration> selectOmgInviteRegistrationList(OmgInviteRegistration omgInviteRegistration)
    {
        return omgInviteRegistrationMapper.selectOmgInviteRegistrationList(omgInviteRegistration);
    }

    /**
     * 新增邀请码注册记录
     * 
     * @param omgInviteRegistration 邀请码注册记录
     * @return 结果
     */
    @Override
    public int insertOmgInviteRegistration(OmgInviteRegistration omgInviteRegistration)
    {
        return omgInviteRegistrationMapper.insertOmgInviteRegistration(omgInviteRegistration);
    }

    /**
     * 修改邀请码注册记录
     * 
     * @param omgInviteRegistration 邀请码注册记录
     * @return 结果
     */
    @Override
    public int updateOmgInviteRegistration(OmgInviteRegistration omgInviteRegistration)
    {
        return omgInviteRegistrationMapper.updateOmgInviteRegistration(omgInviteRegistration);
    }

    /**
     * 批量删除邀请码注册记录
     * 
     * @param ids 需要删除的邀请码注册记录主键
     * @return 结果
     */
    @Override
    public int deleteOmgInviteRegistrationByIds(Long[] ids)
    {
        return omgInviteRegistrationMapper.deleteOmgInviteRegistrationByIds(ids);
    }

    /**
     * 删除邀请码注册记录信息
     * 
     * @param id 邀请码注册记录主键
     * @return 结果
     */
    @Override
    public int deleteOmgInviteRegistrationById(Long id)
    {
        return omgInviteRegistrationMapper.deleteOmgInviteRegistrationById(id);
    }
    //endregion
    @Override
    public QueryWrapper<OmgInviteRegistration> getQueryWrapper(OmgInviteRegistrationQuery omgInviteRegistrationQuery){
        QueryWrapper<OmgInviteRegistration> queryWrapper = new QueryWrapper<>();
        //如果不使用params可以删除
        Map<String, Object> params = omgInviteRegistrationQuery.getParams();
        if (StringUtils.isNull(params)) {
            params = new HashMap<>();
        }
        String inviteCode = omgInviteRegistrationQuery.getInviteCode();
        queryWrapper.eq(StringUtils.isNotEmpty(inviteCode) ,"invite_code",inviteCode);

        Long userId = omgInviteRegistrationQuery.getUserId();
        queryWrapper.eq( StringUtils.isNotNull(userId),"user_id",userId);

        Date registerTime = omgInviteRegistrationQuery.getRegisterTime();
        queryWrapper.eq( StringUtils.isNotNull(registerTime),"register_time",registerTime);

        Date registerDate = omgInviteRegistrationQuery.getRegisterDate();
        queryWrapper.eq( StringUtils.isNotNull(registerDate),"register_date",registerDate);

        return queryWrapper;
    }

    @Override
    public List<OmgInviteRegistrationVo> convertVoList(List<OmgInviteRegistration> omgInviteRegistrationList) {
        if (StringUtils.isEmpty(omgInviteRegistrationList)) {
            return Collections.emptyList();
        }
        return omgInviteRegistrationList.stream().map(OmgInviteRegistrationVo::objToVo).collect(Collectors.toList());
    }

}
