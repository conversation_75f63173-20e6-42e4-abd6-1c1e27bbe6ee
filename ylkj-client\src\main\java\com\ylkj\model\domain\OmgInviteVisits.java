package com.ylkj.model.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 邀请码访问记录实体类
 * 
 * <AUTHOR>
 * @date 2025-01-23
 */
@Data
@TableName("omg_invite_visits")
public class OmgInviteVisits implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /** 主键ID */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /** 邀请码 */
    private String inviteCode;
    
    /** 访问者IP */
    private String visitorIp;
    
    /** 访问时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date visitTime;

    /** 访问日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date visitDate;
}
