package com.ylkj.system.model.dto.omgInviteRegistration;

import java.io.Serializable;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.beans.BeanUtils;
import com.ylkj.system.model.domain.OmgInviteRegistration;
/**
 * 邀请码注册记录Vo对象 omg_invite_registrations
 *
 * <AUTHOR>
 * @date 2025-07-23
 */
@Data
public class OmgInviteRegistrationInsert implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 邀请码 */
    private String inviteCode;

    /** 注册的用户ID */
    private Long userId;

    /** 注册时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date registerTime;

    /** 注册日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date registerDate;

    /**
     * 对象转封装类
     *
     * @param omgInviteRegistrationInsert 插入对象
     * @return OmgInviteRegistrationInsert
     */
    public static OmgInviteRegistration insertToObj(OmgInviteRegistrationInsert omgInviteRegistrationInsert) {
        if (omgInviteRegistrationInsert == null) {
            return null;
        }
        OmgInviteRegistration omgInviteRegistration = new OmgInviteRegistration();
        BeanUtils.copyProperties(omgInviteRegistrationInsert, omgInviteRegistration);
        return omgInviteRegistration;
    }
}
