package com.ylkj.system.service.impl;

import java.util.*;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.stream.Collectors;
import com.ylkj.common.utils.StringUtils;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ylkj.system.mapper.OmgInviteCodesMapper;
import com.ylkj.system.model.domain.OmgInviteCodes;
import com.ylkj.system.service.IOmgInviteCodesService;
import com.ylkj.system.model.dto.omgInviteCodes.OmgInviteCodesQuery;
import com.ylkj.system.model.vo.omgInviteCodes.OmgInviteCodesVo;

/**
 * 邀请码Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
@Service
public class OmgInviteCodesServiceImpl extends ServiceImpl<OmgInviteCodesMapper, OmgInviteCodes> implements IOmgInviteCodesService
{
    @Resource
    private OmgInviteCodesMapper omgInviteCodesMapper;

    //region mybatis代码
    /**
     * 查询邀请码
     * 
     * @param id 邀请码主键
     * @return 邀请码
     */
    @Override
    public OmgInviteCodes selectOmgInviteCodesById(Long id)
    {
        return omgInviteCodesMapper.selectOmgInviteCodesById(id);
    }

    /**
     * 查询邀请码列表
     * 
     * @param omgInviteCodes 邀请码
     * @return 邀请码
     */
    @Override
    public List<OmgInviteCodes> selectOmgInviteCodesList(OmgInviteCodes omgInviteCodes)
    {
        return omgInviteCodesMapper.selectOmgInviteCodesList(omgInviteCodes);
    }

    /**
     * 新增邀请码
     * 
     * @param omgInviteCodes 邀请码
     * @return 结果
     */
    @Override
    public int insertOmgInviteCodes(OmgInviteCodes omgInviteCodes)
    {
        return omgInviteCodesMapper.insertOmgInviteCodes(omgInviteCodes);
    }

    /**
     * 修改邀请码
     * 
     * @param omgInviteCodes 邀请码
     * @return 结果
     */
    @Override
    public int updateOmgInviteCodes(OmgInviteCodes omgInviteCodes)
    {
        return omgInviteCodesMapper.updateOmgInviteCodes(omgInviteCodes);
    }

    /**
     * 批量删除邀请码
     * 
     * @param ids 需要删除的邀请码主键
     * @return 结果
     */
    @Override
    public int deleteOmgInviteCodesByIds(Long[] ids)
    {
        return omgInviteCodesMapper.deleteOmgInviteCodesByIds(ids);
    }

    /**
     * 删除邀请码信息
     * 
     * @param id 邀请码主键
     * @return 结果
     */
    @Override
    public int deleteOmgInviteCodesById(Long id)
    {
        return omgInviteCodesMapper.deleteOmgInviteCodesById(id);
    }
    //endregion
    @Override
    public QueryWrapper<OmgInviteCodes> getQueryWrapper(OmgInviteCodesQuery omgInviteCodesQuery){
        QueryWrapper<OmgInviteCodes> queryWrapper = new QueryWrapper<>();
        //如果不使用params可以删除
        Map<String, Object> params = omgInviteCodesQuery.getParams();
        if (StringUtils.isNull(params)) {
            params = new HashMap<>();
        }
        String inviteCode = omgInviteCodesQuery.getInviteCode();
        queryWrapper.eq(StringUtils.isNotEmpty(inviteCode) ,"invite_code",inviteCode);

        String codeName = omgInviteCodesQuery.getCodeName();
        queryWrapper.like(StringUtils.isNotEmpty(codeName) ,"code_name",codeName);

        String status = omgInviteCodesQuery.getStatus();
        queryWrapper.eq(StringUtils.isNotEmpty(status) ,"status",status);

        Date createdAt = omgInviteCodesQuery.getCreatedAt();
        queryWrapper.eq( StringUtils.isNotNull(createdAt),"created_at",createdAt);

        return queryWrapper;
    }

    @Override
    public List<OmgInviteCodesVo> convertVoList(List<OmgInviteCodes> omgInviteCodesList) {
        if (StringUtils.isEmpty(omgInviteCodesList)) {
            return Collections.emptyList();
        }
        return omgInviteCodesList.stream().map(OmgInviteCodesVo::objToVo).collect(Collectors.toList());
    }

}
