<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ylkj.mapper.ProductMainImagesMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ylkj.model.domain.ProductMainImages">
        <id column="id" property="id" />
        <result column="sku" property="sku" />
        <result column="item_id" property="itemId" />
        <result column="mall_type" property="mallType" />
        <result column="original_image_url" property="originalImageUrl" />
        <result column="oss_image_url" property="ossImageUrl" />
        <result column="image_name" property="imageName" />
        <result column="image_size" property="imageSize" />
        <result column="image_type" property="imageType" />
        <result column="display_order" property="displayOrder" />
        <result column="is_main" property="isMain" />
        <result column="status" property="status" />
        <result column="source" property="source" />
        <result column="remark" property="remark" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
    </resultMap>

    <!-- VO查询映射结果 -->
    <resultMap id="VOResultMap" type="com.ylkj.model.vo.ProductMainImagesVO">
        <id column="id" property="id" />
        <result column="sku" property="sku" />
        <result column="item_id" property="itemId" />
        <result column="mall_type" property="mallType" />
        <result column="original_image_url" property="originalImageUrl" />
        <result column="oss_image_url" property="ossImageUrl" />
        <result column="image_name" property="imageName" />
        <result column="image_size" property="imageSize" />
        <result column="image_type" property="imageType" />
        <result column="display_order" property="displayOrder" />
        <result column="is_main" property="isMain" />
        <result column="status" property="status" />
        <result column="source" property="source" />
        <result column="remark" property="remark" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
        <!-- 扩展字段 -->
        <result column="product_name" property="productName" />
        <result column="product_price" property="productPrice" />
        <result column="image_width" property="imageWidth" />
        <result column="image_height" property="imageHeight" />
        <result column="compressed_image_url" property="compressedImageUrl" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, sku, item_id, mall_type, original_image_url, oss_image_url, image_name, 
        image_size, image_type, display_order, is_main, status, source, remark, 
        create_time, update_time, create_by, update_by
    </sql>

    <!-- 根据商品SKU获取主图列表 -->
    <select id="selectByProductSku" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM omg_product_main_images
        WHERE sku = #{sku}
        AND status = '1'
        ORDER BY is_main DESC, display_order ASC, create_time DESC
    </select>

    <!-- 根据商品ID获取主图列表 -->
    <select id="selectByItemId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM omg_product_main_images
        WHERE item_id = #{itemId}
        AND status = '1'
        ORDER BY is_main DESC, display_order ASC, create_time DESC
    </select>

    <!-- 根据商品SKU获取主图 -->
    <select id="selectMainImageBySku" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM omg_product_main_images
        WHERE sku = #{sku}
        AND is_main = 1
--         AND status = '1'
--         ORDER BY display_order ASC, create_time DESC
        LIMIT 1
    </select>

    <!-- 根据商品ID获取主图 -->
    <select id="selectMainImageByItemId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM omg_product_main_images
        WHERE item_id = #{itemId}
        AND is_main = 1
        AND status = '1'
        ORDER BY display_order ASC, create_time DESC
        LIMIT 1
    </select>

    <!-- 根据商品SKU获取主图VO列表 -->
    <select id="selectVOByProductSku" resultMap="VOResultMap">
        SELECT
            pmi.*,
            p.name as product_name,
            p.price as product_price
        FROM omg_product_main_images pmi
        LEFT JOIN products p ON pmi.sku = p.sku
        WHERE pmi.sku = #{sku}
        AND pmi.status = '1'
        ORDER BY pmi.is_main DESC, pmi.display_order ASC, pmi.create_time DESC
    </select>

    <!-- 根据商品ID获取主图VO列表 -->
    <select id="selectVOByItemId" resultMap="VOResultMap">
        SELECT
            pmi.*,
            p.name as product_name,
            p.price as product_price
        FROM omg_product_main_images pmi
        LEFT JOIN products p ON pmi.item_id = p.item_id
        WHERE pmi.item_id = #{itemId}
        AND pmi.status = '1'
        ORDER BY pmi.is_main DESC, pmi.display_order ASC, pmi.create_time DESC
    </select>

    <!-- 批量插入 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO omg_product_main_images (
            sku, item_id, mall_type, original_image_url, oss_image_url, image_name,
            image_size, image_type, display_order, is_main, status, source, remark,
            create_time, update_time, create_by, update_by
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.sku}, #{item.itemId}, #{item.mallType}, #{item.originalImageUrl}, 
                #{item.ossImageUrl}, #{item.imageName}, #{item.imageSize}, #{item.imageType}, 
                #{item.displayOrder}, #{item.isMain}, #{item.status}, #{item.source}, 
                #{item.remark}, #{item.createTime}, #{item.updateTime}, #{item.createBy}, #{item.updateBy}
            )
        </foreach>
    </insert>

    <!-- 根据商品SKU删除主图 -->
    <delete id="deleteByProductSku">
        DELETE FROM omg_product_main_images WHERE sku = #{sku}
    </delete>

    <!-- 根据商品ID删除主图 -->
    <delete id="deleteByItemId">
        DELETE FROM omg_product_main_images WHERE item_id = #{itemId}
    </delete>

    <!-- 更新主图状态 -->
    <update id="updateStatus">
        UPDATE omg_product_main_images 
        SET status = #{status}, update_time = NOW()
        WHERE id = #{id}
    </update>

    <!-- 设置主图标记 -->
    <update id="setMainImage">
        UPDATE omg_product_main_images 
        SET is_main = CASE WHEN id = #{imageId} THEN 1 ELSE 0 END,
            update_time = NOW()
        WHERE sku = #{sku}
    </update>

    <!-- 根据条件查询主图列表 -->
    <select id="selectByCondition" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM omg_product_main_images
        <where>
            <if test="sku != null and sku != ''">
                AND sku = #{sku}
            </if>
            <if test="itemId != null and itemId != ''">
                AND item_id = #{itemId}
            </if>
            <if test="mallType != null and mallType != ''">
                AND mall_type = #{mallType}
            </if>
            <if test="isMain != null">
                AND is_main = #{isMain}
            </if>
            <if test="status != null and status != ''">
                AND status = #{status}
            </if>
            <if test="source != null and source != ''">
                AND source = #{source}
            </if>
        </where>
        ORDER BY display_order ASC, create_time DESC
    </select>

    <!-- 根据条件查询主图VO列表 -->
    <select id="selectVOByCondition" resultMap="VOResultMap">
        SELECT 
            pmi.*,
            p.name as product_name,
            p.price as product_price
        FROM omg_product_main_images pmi
        LEFT JOIN products p ON pmi.sku = p.sku OR pmi.item_id = p.item_id
        <where>
            <if test="sku != null and sku != ''">
                AND pmi.sku = #{sku}
            </if>
            <if test="itemId != null and itemId != ''">
                AND pmi.item_id = #{itemId}
            </if>
            <if test="mallType != null and mallType != ''">
                AND pmi.mall_type = #{mallType}
            </if>
            <if test="isMain != null">
                AND pmi.is_main = #{isMain}
            </if>
            <if test="status != null and status != ''">
                AND pmi.status = #{status}
            </if>
            <if test="source != null and source != ''">
                AND pmi.source = #{source}
            </if>
        </where>
        ORDER BY pmi.is_main DESC, pmi.display_order ASC, pmi.create_time DESC
    </select>

</mapper>
