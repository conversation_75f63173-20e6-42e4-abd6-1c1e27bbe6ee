package com.ylkj.controller;

import com.ylkj.common.core.domain.AjaxResult;
import com.ylkj.common.utils.ip.IpUtils;
import com.ylkj.service.InviteCodeClientService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * 邀请码客户端控制器
 * 
 * <AUTHOR>
 * @date 2025-01-23
 */
@RestController
@RequestMapping("/front/api/invite")
public class InviteCodeClientController {

    @Autowired
    private InviteCodeClientService inviteCodeClientService;
    
    /**
     * 记录邀请码访问
     *
     * @param inviteCode 邀请码
     * @param request HTTP请求对象
     * @return 操作结果
     */
    @PostMapping("/visit")
    public AjaxResult recordVisit(@RequestParam String inviteCode, HttpServletRequest request) {
        String visitorIp = IpUtils.getIpAddr(request);
        return inviteCodeClientService.recordVisit(inviteCode, visitorIp);
    }
    
    /**
     * 验证邀请码是否有效
     *
     * @param inviteCode 邀请码
     * @return 验证结果
     */
    @GetMapping("/validate/{inviteCode}")
    public AjaxResult validateInviteCode(@PathVariable String inviteCode) {
        return inviteCodeClientService.validateInviteCode(inviteCode);
    }

    /**
     * 记录邀请码注册
     *
     * @param requestBody 包含邀请码和用户ID的JSON对象
     * @return 操作结果
     */
    @PostMapping("/register")
    public AjaxResult recordRegistration(@RequestBody java.util.Map<String, Object> requestBody) {
        return inviteCodeClientService.recordRegistration(requestBody);
    }
}
