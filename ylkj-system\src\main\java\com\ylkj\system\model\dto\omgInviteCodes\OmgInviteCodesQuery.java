package com.ylkj.system.model.dto.omgInviteCodes;

import java.util.Map;
import java.io.Serializable;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import org.springframework.beans.BeanUtils;
import com.baomidou.mybatisplus.annotation.TableField;
import com.ylkj.system.model.domain.OmgInviteCodes;
/**
 * 邀请码Query对象 omg_invite_codes
 *
 * <AUTHOR>
 * @date 2025-07-23
 */
@Data
public class OmgInviteCodesQuery implements Serializable
{
    private static final long serialVersionUID = 1L;
    private Long id;
    /** 邀请码 */
    private String inviteCode;

    /** 邀请码名称 */
    private String codeName;

    /** 状态：active/inactive */
    private String status;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date createdAt;

    /** 请求参数 */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @TableField(exist = false)
    private Map<String, Object> params;

    /**
     * 对象转封装类
     *
     * @param omgInviteCodesQuery 查询对象
     * @return OmgInviteCodes
     */
    public static OmgInviteCodes queryToObj(OmgInviteCodesQuery omgInviteCodesQuery) {
        if (omgInviteCodesQuery == null) {
            return null;
        }
        OmgInviteCodes omgInviteCodes = new OmgInviteCodes();
        BeanUtils.copyProperties(omgInviteCodesQuery, omgInviteCodes);
        return omgInviteCodes;
    }
}
