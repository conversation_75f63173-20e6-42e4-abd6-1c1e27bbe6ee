package com.ylkj.system.model.vo.omgInviteCodes;

import java.io.Serializable;
import java.util.Date;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import com.ylkj.common.annotation.Excel;
import org.springframework.beans.BeanUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ylkj.system.model.domain.OmgInviteCodes;
/**
 * 邀请码Vo对象 omg_invite_codes
 *
 * <AUTHOR>
 * @date 2025-07-23
 */
@Data
public class OmgInviteCodesVo implements Serializable
{
    private static final long serialVersionUID = 1L;
    private Long id;
    /** 邀请码 */
    @Excel(name = "邀请码")
    private String inviteCode;

    /** 邀请码名称 */
    @Excel(name = "邀请码名称")
    private String codeName;

    /** 总访问次数 */
    @Excel(name = "总访问次数")
    private Long totalVisits;

    /** 总注册人数 */
    @Excel(name = "总注册人数")
    private Long totalRegistrations;

    /** 状态：active/inactive */
    @Excel(name = "状态：active/inactive")
    private String status;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createdAt;


     /**
     * 对象转封装类
     *
     * @param omgInviteCodes OmgInviteCodes实体对象
     * @return OmgInviteCodesVo
     */
    public static OmgInviteCodesVo objToVo(OmgInviteCodes omgInviteCodes) {
        if (omgInviteCodes == null) {
            return null;
        }
        OmgInviteCodesVo omgInviteCodesVo = new OmgInviteCodesVo();
        BeanUtils.copyProperties(omgInviteCodes, omgInviteCodesVo);
        return omgInviteCodesVo;
    }
}
