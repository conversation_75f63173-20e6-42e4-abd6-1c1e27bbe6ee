package com.ylkj.system.model.dto.omgProducts;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 主图操作DTO对象
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="MainImageOperationDTO对象", description="主图操作DTO")
public class MainImageOperationDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "图片ID", required = true)
    @NotNull(message = "图片ID不能为空")
    private Long imageId;

    @ApiModelProperty(value = "商品SKU")
    private String sku;

    @ApiModelProperty(value = "操作类型(SET-设置主图, CANCEL-取消主图)")
    private String operationType;

    @ApiModelProperty(value = "操作原因")
    private String reason;

    @ApiModelProperty(value = "批量操作的图片ID列表")
    private List<Long> imageIds;

    @ApiModelProperty(value = "是否强制操作(忽略警告)")
    private Boolean forceOperation;

    @ApiModelProperty(value = "操作者")
    private String operator;

    // 构造方法
    public MainImageOperationDTO() {}

    public MainImageOperationDTO(Long imageId) {
        this.imageId = imageId;
    }

    public MainImageOperationDTO(Long imageId, String operationType) {
        this.imageId = imageId;
        this.operationType = operationType;
    }

    public MainImageOperationDTO(List<Long> imageIds, String operationType) {
        this.imageIds = imageIds;
        this.operationType = operationType;
    }

    // 静态工厂方法
    public static MainImageOperationDTO createSetOperation(Long imageId) {
        return new MainImageOperationDTO(imageId, "SET");
    }

    public static MainImageOperationDTO createCancelOperation(Long imageId) {
        return new MainImageOperationDTO(imageId, "CANCEL");
    }

    public static MainImageOperationDTO createBatchSetOperation(List<Long> imageIds) {
        return new MainImageOperationDTO(imageIds, "BATCH_SET");
    }

    // 验证方法
    public boolean isValidForSingleOperation() {
        return imageId != null;
    }

    public boolean isValidForBatchOperation() {
        return imageIds != null && !imageIds.isEmpty();
    }

    public boolean isSetOperation() {
        return "SET".equals(operationType) || "BATCH_SET".equals(operationType);
    }

    public boolean isCancelOperation() {
        return "CANCEL".equals(operationType);
    }

    public boolean isBatchOperation() {
        return "BATCH_SET".equals(operationType);
    }
}
