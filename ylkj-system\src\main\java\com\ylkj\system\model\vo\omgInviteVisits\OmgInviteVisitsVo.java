package com.ylkj.system.model.vo.omgInviteVisits;

import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ylkj.system.model.domain.OmgInviteVisit;
import lombok.Data;
import com.ylkj.common.annotation.Excel;
import org.springframework.beans.BeanUtils;

/**
 * 邀请码访问记录Vo对象 omg_invite_visits
 *
 * <AUTHOR>
 * @date 2025-07-23
 */
@Data
public class OmgInviteVisitsVo implements Serializable
{
    private static final long serialVersionUID = 1L;
    private Long id;
    /** 邀请码 */
    @Excel(name = "邀请码")
    private String inviteCode;

    /** 访问者IP */
    @Excel(name = "访问者IP")
    private String visitorIp;

    /** 访问时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "访问时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date visitTime;

    /** 访问日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "访问日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date visitDate;


     /**
     * 对象转封装类
     *
     * @param omgInviteVisit OmgInviteVisits实体对象
     * @return OmgInviteVisitsVo
     */
    public static OmgInviteVisitsVo objToVo(OmgInviteVisit omgInviteVisit) {
        if (omgInviteVisit == null) {
            return null;
        }
        OmgInviteVisitsVo omgInviteVisitsVo = new OmgInviteVisitsVo();
        BeanUtils.copyProperties(omgInviteVisit, omgInviteVisitsVo);
        return omgInviteVisitsVo;
    }
}
