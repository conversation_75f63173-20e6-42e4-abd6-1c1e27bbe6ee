package com.ylkj.model.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 商品主图VO对象
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="ProductMainImagesVO对象", description="商品主图VO")
public class ProductMainImagesVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "商品SKU")
    private String sku;

    @ApiModelProperty(value = "商品ID")
    private String itemId;

    @ApiModelProperty(value = "商城类型")
    private String mallType;

    @ApiModelProperty(value = "原始图片URL")
    private String originalImageUrl;

    @ApiModelProperty(value = "OSS图片URL")
    private String ossImageUrl;

    @ApiModelProperty(value = "图片文件名")
    private String imageName;

    @ApiModelProperty(value = "图片大小(字节)")
    private Long imageSize;

    @ApiModelProperty(value = "图片类型")
    private String imageType;

    @ApiModelProperty(value = "显示顺序")
    private Integer displayOrder;

    @ApiModelProperty(value = "是否为主图(0-否,1-是)")
    private Integer isMain;

    @ApiModelProperty(value = "状态(0-禁用,1-启用)")
    private String status;

    @ApiModelProperty(value = "图片来源")
    private String source;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @ApiModelProperty(value = "创建者")
    private String createBy;

    @ApiModelProperty(value = "更新者")
    private String updateBy;

    // 扩展字段
    @ApiModelProperty(value = "商品名称")
    private String productName;

    @ApiModelProperty(value = "商品价格")
    private String productPrice;

    @ApiModelProperty(value = "图片宽度")
    private Integer imageWidth;

    @ApiModelProperty(value = "图片高度")
    private Integer imageHeight;

    @ApiModelProperty(value = "压缩后图片URL")
    private String compressedImageUrl;
}
