package com.ylkj.service.impl;

import com.ylkj.common.core.domain.AjaxResult;
import com.ylkj.mapper.OmgInviteCodesClientMapper;
import com.ylkj.mapper.OmgInviteRegistrationsClientMapper;
import com.ylkj.mapper.OmgInviteVisitsClientMapper;
import com.ylkj.model.domain.OmgInviteRegistrations;
import com.ylkj.model.domain.OmgInviteVisits;
import com.ylkj.service.InviteCodeClientService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.Map;

/**
 * 邀请码客户端服务实现类
 * 
 * <AUTHOR>
 * @date 2025-01-23
 */
@Service
public class InviteCodeClientServiceImpl implements InviteCodeClientService {
    
    private static final Logger logger = LoggerFactory.getLogger(InviteCodeClientServiceImpl.class);
    
    @Autowired
    private OmgInviteCodesClientMapper inviteCodesMapper;

    @Autowired
    private OmgInviteVisitsClientMapper inviteVisitsMapper;

    @Autowired
    private OmgInviteRegistrationsClientMapper inviteRegistrationsMapper;
    
    /**
     * 记录邀请码访问
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult recordVisit(String inviteCode, String visitorIp) {
        try {
            logger.info("开始记录邀请码访问: inviteCode={}, visitorIp={}", inviteCode, visitorIp);

            // 1. 验证邀请码是否存在且有效
            if (!validateInviteCodeInternal(inviteCode)) {
                return AjaxResult.error("邀请码不存在或已失效");
            }

            // 2. 防刷检查：同一IP在10分钟内不重复统计
            int duplicateCount = inviteVisitsMapper.checkDuplicateVisit(inviteCode, visitorIp, 10);
            if (duplicateCount > 0) {
                logger.info("检测到重复访问，跳过统计: inviteCode={}, visitorIp={}", inviteCode, visitorIp);
                return AjaxResult.success("访问记录成功");
            }

            // 3. 记录访问详情
            OmgInviteVisits visit = new OmgInviteVisits();
            visit.setInviteCode(inviteCode);
            visit.setVisitorIp(visitorIp);
            visit.setVisitTime(new Date());
            visit.setVisitDate(new Date());

            int insertResult = inviteVisitsMapper.insert(visit);
            if (insertResult <= 0) {
                return AjaxResult.error("记录访问详情失败");
            }

            // 实时更新主表统计：通过查询详情表来更新
            updateVisitsStatsByQuery(inviteCode);

            logger.info("邀请码访问记录成功: inviteCode={}, visitorIp={}", inviteCode, visitorIp);
            return AjaxResult.success("访问记录成功");

        } catch (Exception e) {
            logger.error("记录邀请码访问失败: inviteCode={}, visitorIp={}", inviteCode, visitorIp, e);
            return AjaxResult.error("记录访问失败: " + e.getMessage());
        }
    }
    
    /**
     * 记录邀请码注册
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult recordRegistration(Map<String, Object> requestBody) {
        try {
            // 参数解析和验证
            String inviteCode = (String) requestBody.get("inviteCode");
            Object userIdObj = requestBody.get("userId");

            if (StringUtils.isBlank(inviteCode)) {
                return AjaxResult.error("邀请码不能为空");
            }

            if (userIdObj == null) {
                return AjaxResult.error("用户ID不能为空");
            }

            Long userId;
            try {
                if (userIdObj instanceof String) {
                    userId = Long.parseLong((String) userIdObj);
                } else if (userIdObj instanceof Number) {
                    userId = ((Number) userIdObj).longValue();
                } else {
                    return AjaxResult.error("用户ID格式错误");
                }
            } catch (NumberFormatException e) {
                return AjaxResult.error("用户ID格式错误");
            }

            logger.info("开始记录邀请码注册: inviteCode={}, userId={}", inviteCode, userId);

            // 1. 验证邀请码是否存在且有效
            if (!validateInviteCodeInternal(inviteCode)) {
                logger.warn("邀请码不存在或已失效，跳过注册统计: inviteCode={}", inviteCode);
                return AjaxResult.error("邀请码不存在或已失效");
            }

            // 2. 检查是否已经记录过该用户的注册
            int existingCount = inviteRegistrationsMapper.checkUserRegistration(inviteCode, userId);
            if (existingCount > 0) {
                logger.info("用户已通过该邀请码注册过，跳过重复统计: inviteCode={}, userId={}", inviteCode, userId);
                return AjaxResult.success("注册记录成功");
            }

            // 3. 记录注册详情
            OmgInviteRegistrations registration = new OmgInviteRegistrations();
            registration.setInviteCode(inviteCode);
            registration.setUserId(userId);
            registration.setRegisterTime(new Date());
            registration.setRegisterDate(new Date());

            int insertResult = inviteRegistrationsMapper.insert(registration);
            if (insertResult <= 0) {
                return AjaxResult.error("记录注册详情失败");
            }

            // 实时更新主表统计：通过查询详情表来更新
            updateRegistrationsStatsByQuery(inviteCode);

            logger.info("邀请码注册记录成功: inviteCode={}, userId={}", inviteCode, userId);
            return AjaxResult.success("注册记录成功");

        } catch (Exception e) {
            logger.error("记录邀请码注册失败", e);
            return AjaxResult.error("记录注册失败: " + e.getMessage());
        }
    }
    
    /**
     * 验证邀请码是否有效
     */
    @Override
    public AjaxResult validateInviteCode(String inviteCode) {
        try {
            logger.info("验证邀请码: {}", inviteCode);

            boolean isValid = validateInviteCodeInternal(inviteCode);

            if (isValid) {
                return AjaxResult.success("邀请码有效");
            } else {
                return AjaxResult.error("邀请码无效或已失效");
            }
        } catch (Exception e) {
            logger.error("验证邀请码失败: {}", e.getMessage(), e);
            return AjaxResult.error("验证邀请码失败: " + e.getMessage());
        }
    }

    /**
     * 验证邀请码是否有效
     */
    private boolean validateInviteCodeInternal(String inviteCode) {
        // 1. 使用 StringUtils.isBlank() 检查空值（包括null、空字符串、空白字符）
        if (StringUtils.isBlank(inviteCode)) {
            return false;
        }

        // 2. 去除前后空格
        inviteCode = inviteCode.trim();

        // 3. 长度检查
        if (inviteCode.length() > 50) {
            logger.warn("邀请码长度超限: length={}", inviteCode.length());
            return false;
        }

        // 4. 格式检查（只允许字母数字）
        if (!inviteCode.matches("^[A-Za-z0-9]+$")) {
            logger.warn("邀请码格式错误: inviteCode={}", inviteCode);
            return false;
        }

        try {
            // 5. 数据库验证
            int count = inviteCodesMapper.validateInviteCode(inviteCode);
            return count > 0;
        } catch (Exception e) {
            logger.error("验证邀请码时发生数据库异常: inviteCode={}", inviteCode, e);
            return false;
        }
    }

    /**
     * 通过查询详情表来更新访问量统计
     */
    private void updateVisitsStatsByQuery(String inviteCode) {
        try {
            // 实时查询当前总访问量
            int currentTotalVisits = inviteCodesMapper.getTotalVisits(inviteCode);

            // 更新主表统计字段
            int updateResult = inviteCodesMapper.updateVisitsStats(inviteCode, currentTotalVisits);
            if (updateResult <= 0) {
                logger.warn("更新访问量统计失败: inviteCode={}, totalVisits={}", inviteCode, currentTotalVisits);
            } else {
                logger.debug("访问量统计更新成功: inviteCode={}, totalVisits={}", inviteCode, currentTotalVisits);
            }
        } catch (Exception e) {
            logger.error("更新访问量统计异常: inviteCode={}", inviteCode, e);
            // 不抛出异常，避免影响主业务流程
        }
    }

    /**
     * 通过查询详情表来更新注册数统计
     */
    private void updateRegistrationsStatsByQuery(String inviteCode) {
        try {
            // 实时查询当前总注册数
            int currentTotalRegistrations = inviteCodesMapper.getTotalRegistrations(inviteCode);

            // 更新主表统计字段
            int updateResult = inviteCodesMapper.updateRegistrationsStats(inviteCode, currentTotalRegistrations);
            if (updateResult <= 0) {
                logger.warn("更新注册数统计失败: inviteCode={}, totalRegistrations={}", inviteCode, currentTotalRegistrations);
            } else {
                logger.debug("注册数统计更新成功: inviteCode={}, totalRegistrations={}", inviteCode, currentTotalRegistrations);
            }
        } catch (Exception e) {
            logger.error("更新注册数统计异常: inviteCode={}", inviteCode, e);
            // 不抛出异常，避免影响主业务流程
        }
    }
}
