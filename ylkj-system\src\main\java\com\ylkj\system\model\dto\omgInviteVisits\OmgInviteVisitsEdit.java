package com.ylkj.system.model.dto.omgInviteVisits;

import java.io.Serializable;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ylkj.system.model.domain.OmgInviteVisit;
import lombok.Data;
import org.springframework.beans.BeanUtils;

/**
 * 邀请码访问记录Vo对象 omg_invite_visits
 *
 * <AUTHOR>
 * @date 2025-07-23
 */
@Data
public class OmgInviteVisitsEdit implements Serializable
{
    private static final long serialVersionUID = 1L;
    private Long id;
    /** 邀请码 */
    private String inviteCode;

    /** 访问者IP */
    private String visitorIp;

    /** 访问时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date visitTime;

    /** 访问日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date visitDate;

    /**
     * 对象转封装类
     *
     * @param omgInviteVisitsEdit 编辑对象
     * @return OmgInviteVisit
     */
    public static OmgInviteVisit editToObj(OmgInviteVisitsEdit omgInviteVisitsEdit) {
        if (omgInviteVisitsEdit == null) {
            return null;
        }
        OmgInviteVisit omgInviteVisit = new OmgInviteVisit();
        BeanUtils.copyProperties(omgInviteVisitsEdit, omgInviteVisit);
        return omgInviteVisit;
    }
}
