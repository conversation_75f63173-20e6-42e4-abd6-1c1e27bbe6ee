package com.ylkj.system.model.domain;

import java.io.Serializable;
import java.util.Map;
import java.util.Date;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.ylkj.common.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
/**
 * 邀请码对象 omg_invite_codes
 *
 * <AUTHOR>
 * @date 2025-07-23
 */
@TableName("omg_invite_codes")
@Data
public class OmgInviteCodes implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /** 邀请码 */
    @Excel(name = "邀请码")
    private String inviteCode;

    /** 邀请码名称 */
    @Excel(name = "邀请码名称")
    private String codeName;

    /** 总访问次数 */
    @Excel(name = "总访问次数")
    private Long totalVisits;

    /** 总注册人数 */
    @Excel(name = "总注册人数")
    private Long totalRegistrations;

    /** 状态：active/inactive */
    @Excel(name = "状态：active/inactive")
    private String status;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createdAt;

    /** 请求参数 */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @TableField(exist = false)
    private Map<String, Object> params;
}
